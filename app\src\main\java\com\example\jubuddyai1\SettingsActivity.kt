package com.example.jubuddyai1

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.firebase.auth.FirebaseAuth
import com.example.jubuddyai1.ads.AdManager
import com.example.jubuddyai1.ads.NativeAdManager

class SettingsActivity : AppCompatActivity() {
    
    private lateinit var signOutButton: Button
    private lateinit var backButton: Button
    private val TAG = "SettingsActivity"
    private lateinit var adManager: AdManager
    private lateinit var nativeAdManager: NativeAdManager
    private val NATIVE_AD_ID = "ca-app-pub-3639460814030294/3631252078"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)
        
        // Hide system UI for immersive experience
        hideSystemUI()
        
        // Initialize buttons
        signOutButton = findViewById(R.id.signOutButton)
        backButton = findViewById(R.id.backButton)
        
        // Initialize AdManager
        adManager = AdManager(this)
        adManager.initialize()
        
        // Initialize the native ad manager
        nativeAdManager = NativeAdManager(this)
        
        // Load banner ad
        val adContainer = findViewById<FrameLayout>(R.id.adContainer)
        adManager.loadBannerAd(adContainer)
        
        // Load native ad
        nativeAdManager.loadNativeAd(adContainer, NATIVE_AD_ID)
        
        // Set up click listeners
        signOutButton.setOnClickListener {
            signOut()
        }
        
        backButton.setOnClickListener {
            finish() // Return to previous activity
        }
    }
    
    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }
    
    private fun signOut() {
        try {
            Log.d(TAG, "Signing out user")
            
            // Sign out from Firebase
            FirebaseAuth.getInstance().signOut()
            
            // Sign out from Google
            val googleSignInClient = GoogleSignIn.getClient(this, 
                GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).build())
            googleSignInClient.signOut()
            
            // Clear user data but KEEP terms acceptance
            val sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
            sharedPreferences.edit().apply {
                remove("userEmail")
                remove("userName")
                remove("authMethod")
                // Deliberately NOT clearing termsAccepted
                apply()
            }
            
            Log.d(TAG, "Sign out complete, redirecting to SignInActivity")
            
            // Return to the sign-in screen
            val intent = Intent(this, SignInActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error signing out: ${e.message}", e)
        }
    }
    
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }
    
    override fun onDestroy() {
        adManager.destroy()
        nativeAdManager.destroy()
        super.onDestroy()
    }
} 