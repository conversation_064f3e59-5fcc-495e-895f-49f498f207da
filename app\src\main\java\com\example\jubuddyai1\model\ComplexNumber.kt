package com.example.jubuddyai1.model

import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import kotlin.math.*
import kotlin.math.log10

// Extension operators for BigDecimal
operator fun BigDecimal.plus(other: BigDecimal): BigDecimal = this.add(other)
operator fun BigDecimal.minus(other: BigDecimal): BigDecimal = this.subtract(other)
operator fun BigDecimal.times(other: BigDecimal): BigDecimal = this.multiply(other)
operator fun BigDecimal.div(other: BigDecimal): BigDecimal = this.divide(other, MathContext(100, RoundingMode.HALF_UP))

data class ComplexNumber(
    val real: BigDecimal,
    val imag: BigDecimal,
    private val mathContext: MathContext = MathContext(100, RoundingMode.HALF_UP)
) {
    fun conjugate(): ComplexNumber = ComplexNumber(real, -imag, mathContext)

    fun abs(): BigDecimal = sqrt(real * real + imag * imag)

    // Custom square root implementation using <PERSON>'s method
    // This replaces the BigDecimal.sqrt() which requires API level 33
    private fun sqrt(value: BigDecimal): BigDecimal {
        if (value.compareTo(BigDecimal.ZERO) <= 0) {
            return if (value.compareTo(BigDecimal.ZERO) == 0) BigDecimal.ZERO else throw ArithmeticException("Square root of negative number")
        }
        
        var x = BigDecimal(Math.sqrt(value.toDouble()).toString(), mathContext)
        val TWO = BigDecimal("2")
        
        // Newton's method: x = (x + n/x) / 2
        for (i in 0 until 10) { // 10 iterations should be enough for high precision
            x = (x.add(value.divide(x, mathContext))).divide(TWO, mathContext)
        }
        
        return x
    }

    fun arg(): BigDecimal = BigDecimal(atan2(imag.toDouble(), real.toDouble()).toString(), mathContext)

    operator fun plus(other: ComplexNumber): ComplexNumber =
        ComplexNumber(real + other.real, imag + other.imag, mathContext)

    operator fun minus(other: ComplexNumber): ComplexNumber =
        ComplexNumber(real - other.real, imag - other.imag, mathContext)

    operator fun times(other: ComplexNumber): ComplexNumber =
        ComplexNumber(
            real * other.real - imag * other.imag,
            real * other.imag + imag * other.real,
            mathContext
        )

    operator fun div(other: ComplexNumber): ComplexNumber {
        val denominator = other.real * other.real + other.imag * other.imag
        return ComplexNumber(
            (real * other.real + imag * other.imag) / denominator,
            (imag * other.real - real * other.imag) / denominator,
            mathContext
        )
    }

    fun exp(): ComplexNumber {
        val expReal = BigDecimal(exp(real.toDouble()).toString(), mathContext)
        return ComplexNumber(
            expReal * BigDecimal(cos(imag.toDouble()).toString(), mathContext),
            expReal * BigDecimal(sin(imag.toDouble()).toString(), mathContext),
            mathContext
        )
    }

    fun ln(): ComplexNumber = ComplexNumber(
        BigDecimal(kotlin.math.ln(abs().toDouble()).toString(), mathContext),
        arg(),
        mathContext
    )

    companion object {
        val ZERO = ComplexNumber(BigDecimal.ZERO, BigDecimal.ZERO)
        val ONE = ComplexNumber(BigDecimal.ONE, BigDecimal.ZERO)
        val I = ComplexNumber(BigDecimal.ZERO, BigDecimal.ONE)
    }
}