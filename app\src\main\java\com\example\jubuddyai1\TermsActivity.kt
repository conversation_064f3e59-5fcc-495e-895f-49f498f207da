package com.example.jubuddyai1

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.CheckBox
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class TermsActivity : AppCompatActivity() {
    
    private lateinit var sharedPreferences: SharedPreferences
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_terms)
        
        sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
        
        val termsText = findViewById<TextView>(R.id.termsText)
        val acceptCheckbox = findViewById<CheckBox>(R.id.acceptCheckbox)
        val continueButton = findViewById<Button>(R.id.continueButton)
        val scrollView = findViewById<ScrollView>(R.id.termsScrollView)
        
        // Set terms and conditions text
        termsText.text = """
            JU Buddy AI – Terms and Conditions

            Welcome to JU Buddy AI, your all-in-one assistant for Jadavpur University. Before using the app, please read and accept the following Terms and Conditions:

            1. Acceptance of Terms
            By using JU Buddy AI, you confirm that you have read, understood, and agree to comply with these Terms and Conditions.


            2. Eligibility
            JU Buddy AI is intended for students, faculty, and official members of Jadavpur University. Any unauthorized access or misuse is strictly prohibited.


            3. Purpose and Usage
            JU Buddy AI is developed to assist users with academic queries, university-related services, and general support. You agree not to use the app for illegal, harmful, or unethical activities.


            4. Accuracy of Information
            While JU Buddy AI strives to deliver accurate and helpful responses (including those powered by DeepSeek AI), we do not guarantee complete correctness. Users are responsible for verifying any critical information.


            5. Data Privacy
            JU Buddy AI values your privacy. Minimal data may be collected to enhance performance (such as usage patterns and app preferences), but no personal or sensitive data is stored or shared with third parties.


            6. Intellectual Property
            All content within JU Buddy AI—including design, features, and AI-generated content—is the property of the app's creators. Reproduction, modification, or redistribution without written permission is not allowed.


            7. Third-party Integrations
            JU Buddy AI integrates third-party services like DeepSeek AI. We are not responsible for issues related to these external services, including availability or accuracy.


            8. Modifications and Updates
            We reserve the right to update these terms and make changes to JU Buddy AI features at any time. Continued use of the app after such changes indicates your acceptance.


            9. Limitation of Liability
            JU Buddy AI is provided "as is" without warranties of any kind. The developers are not liable for any damages, losses, or consequences resulting from the app's use.


            10. Termination of Access
            Any violation of these terms, abuse of services, or suspicious behavior may lead to temporary or permanent suspension of access to JU Buddy AI.
        """.trimIndent()
        
        continueButton.setOnClickListener {
            if (acceptCheckbox.isChecked) {
                acceptTerms()
            } else {
                Toast.makeText(this, "Please accept the terms and conditions to continue", Toast.LENGTH_SHORT).show()
                
                // Auto-scroll to the checkbox to draw attention to it
                scrollView.post {
                    scrollView.smoothScrollTo(0, acceptCheckbox.top)
                }
            }
        }
    }

    private fun acceptTerms() {
        // Save the acceptance state to shared preferences
        val sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
        sharedPreferences.edit().apply {
            putBoolean("termsAccepted", true)
            apply()
        }
        
        // Log acceptance for debugging
        Log.d("TermsActivity", "Terms accepted, saved to preferences")
        
        // Navigate to MainActivity
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
} 