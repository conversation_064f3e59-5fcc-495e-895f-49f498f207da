package com.example.jubuddyai1

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.auth.FirebaseAuth
import java.util.regex.Pattern

class ForgotPasswordActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "ForgotPasswordActivity"
    }
    
    private lateinit var emailInput: EditText
    private lateinit var resetButton: Button
    private lateinit var backButton: ImageView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_forgot_password)
        
        // Hide system UI
        hideSystemUI()
        
        // Initialize views
        emailInput = findViewById(R.id.emailInput)
        resetButton = findViewById(R.id.resetButton)
        backButton = findViewById(R.id.backButton)
        
        // Set up click listeners
        resetButton.setOnClickListener {
            val email = emailInput.text.toString().trim()
            if (email.isEmpty()) {
                emailInput.error = "Email is required"
                return@setOnClickListener
            }
            
            if (!isValidEmail(email)) {
                emailInput.error = "Enter a valid email address"
                return@setOnClickListener
            }
            
            resetPassword(email)
        }
        
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun resetPassword(email: String) {
        // Show loading (you could add a progress bar)
        resetButton.isEnabled = false
        
        FirebaseAuth.getInstance().sendPasswordResetEmail(email)
            .addOnCompleteListener { task ->
                resetButton.isEnabled = true
                
                if (task.isSuccessful) {
                    Toast.makeText(this, "Reset link sent to your email", Toast.LENGTH_LONG).show()
                    // Return to the sign in screen after a short delay
                    resetButton.postDelayed({ finish() }, 2000)
                } else {
                    Log.e(TAG, "Reset password failed", task.exception)
                    Toast.makeText(this, "Failed to send reset email: ${task.exception?.message}", 
                        Toast.LENGTH_LONG).show()
                }
            }
    }
    
    private fun isValidEmail(email: String): Boolean {
        val emailPattern = Pattern.compile(
            "[a-zA-Z0-9+._%\\-]{1,256}" +
                    "@" +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                    "(" +
                    "\\." +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                    ")+"
        )
        return emailPattern.matcher(email).matches()
    }
    
    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }
    
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }
} 