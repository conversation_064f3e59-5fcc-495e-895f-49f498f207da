<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <ImageView
        android:id="@+id/avatarImage"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ju_logo"
        android:visibility="gone"
        android:layout_marginStart="4dp"
        android:padding="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/messageCard"
        android:adjustViewBounds="true"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/messageCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="#000000"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/avatarImage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.8"
        app:layout_constraintHorizontal_bias="0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <ImageView
                android:id="@+id/imagePreview"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:scaleType="centerCrop"
                android:adjustViewBounds="true"
                android:visibility="gone"
                android:layout_marginBottom="8dp"/>

            <TextView
                android:id="@+id/messageText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:autoLink="web|email|phone"
                android:textIsSelectable="true"
                android:textColorLink="@color/linkColor"
                android:lineSpacingMultiplier="1.2"
                android:padding="12dp" />

            <WebView
                android:id="@+id/messageWebView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <include
                android:id="@+id/typingIndicator"
                layout="@layout/typing_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
