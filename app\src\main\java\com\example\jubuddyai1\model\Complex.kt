package com.example.jubuddyai1.model

import kotlin.math.*

/**
 * Complex number implementation for advanced mathematical operations.
 * Supports all standard complex number operations including arithmetic,
 * trigonometric, hyperbolic, logarithmic, and exponential functions.
 */
class Complex(val real: Double, val imaginary: Double) {
    
    companion object {
        val ZERO = Complex(0.0, 0.0)
        val ONE = Complex(1.0, 0.0)
        val I = Complex(0.0, 1.0)
        
        // Create a complex number from polar coordinates (r, θ)
        fun fromPolar(r: Double, theta: Double): Complex {
            return Complex(r * cos(theta), r * sin(theta))
        }
    }
    
    // Basic arithmetic operations
    fun add(other: Complex): Complex = Complex(real + other.real, imaginary + other.imaginary)
    fun subtract(other: Complex): Complex = Complex(real - other.real, imaginary - other.imaginary)
    fun multiply(other: Complex): Complex {
        val newReal = real * other.real - imaginary * other.imaginary
        val newImaginary = real * other.imaginary + imaginary * other.real
        return Complex(newReal, newImaginary)
    }
    
    fun divide(other: Complex): Complex {
        val denominator = other.real * other.real + other.imaginary * other.imaginary
        if (denominator == 0.0) throw ArithmeticException("Division by zero")
        
        val newReal = (real * other.real + imaginary * other.imaginary) / denominator
        val newImaginary = (imaginary * other.real - real * other.imaginary) / denominator
        return Complex(newReal, newImaginary)
    }
    
    // Unary operations
    fun conjugate(): Complex = Complex(real, -imaginary)
    fun negate(): Complex = Complex(-real, -imaginary)
    
    // Magnitude and argument
    fun abs(): Double = sqrt(real * real + imaginary * imaginary)
    fun arg(): Double = atan2(imaginary, real)
    fun norm(): Double = real * real + imaginary * imaginary
    
    // Powers and roots
    fun pow(n: Double): Complex {
        if (real == 0.0 && imaginary == 0.0) {
            return if (n == 0.0) ONE else ZERO
        }
        
        val r = abs()
        val theta = arg()
        val newR = r.pow(n)
        val newTheta = n * theta
        
        return fromPolar(newR, newTheta)
    }
    
    fun sqrt(): Complex = pow(0.5)
    
    // Exponential and logarithmic functions
    fun exp(): Complex {
        val expReal = exp(real)
        return Complex(expReal * cos(imaginary), expReal * sin(imaginary))
    }
    
    fun ln(): Complex {
        val r = abs()
        val theta = arg()
        return Complex(ln(r), theta)
    }
    
    // Trigonometric functions
    fun sin(): Complex {
        return Complex(sin(real) * cosh(imaginary), cos(real) * sinh(imaginary))
    }
    
    fun cos(): Complex {
        return Complex(cos(real) * cosh(imaginary), -sin(real) * sinh(imaginary))
    }
    
    fun tan(): Complex {
        val denominator = cos().norm()
        if (denominator == 0.0) throw ArithmeticException("Division by zero in tan")
        return sin().divide(cos())
    }
    
    // Hyperbolic functions
    fun sinh(): Complex {
        return Complex(sinh(real) * cos(imaginary), cosh(real) * sin(imaginary))
    }
    
    fun cosh(): Complex {
        return Complex(cosh(real) * cos(imaginary), sinh(real) * sin(imaginary))
    }
    
    fun tanh(): Complex {
        val denominator = cosh().norm()
        if (denominator == 0.0) throw ArithmeticException("Division by zero in tanh")
        return sinh().divide(cosh())
    }
    
    // Inverse trigonometric functions
    fun asin(): Complex {
        return multiply(I).add(ONE.subtract(multiply(this)).sqrt()).ln().multiply(Complex(0.0, -1.0))
    }
    
    fun acos(): Complex {
        return Complex(PI/2, 0.0).subtract(asin())
    }
    
    fun atan(): Complex {
        val i = Complex(0.0, 1.0)
        val iPlus = i.add(this)
        val iMinus = i.subtract(this)
        return i.multiply(iPlus.divide(iMinus).ln()).multiply(Complex(0.5, 0.0))
    }
    
    // Inverse hyperbolic functions
    fun asinh(): Complex {
        return add(multiply(this).add(ONE).sqrt()).ln()
    }
    
    fun acosh(): Complex {
        return add(multiply(this).subtract(ONE).sqrt()).ln()
    }
    
    fun atanh(): Complex {
        val one = Complex(1.0, 0.0)
        val oneMinusThis = one.subtract(this)
        if (oneMinusThis.norm() == 0.0) throw ArithmeticException("Division by zero in atanh")
        return one.add(this).divide(oneMinusThis).ln().multiply(Complex(0.5, 0.0))
    }
    
    // Special functions for quantum mechanics
    fun hermitianConjugate(): Complex = conjugate()
    
    fun innerProduct(other: Complex): Double {
        return real * other.real + imaginary * other.imaginary
    }
    
    fun outerProduct(other: Complex): Complex {
        return multiply(other.conjugate())
    }
    
    // Utility methods
    override fun toString(): String {
        return when {
            imaginary == 0.0 -> real.toString()
            real == 0.0 -> "${imaginary}i"
            imaginary > 0 -> "$real + ${imaginary}i"
            else -> "$real - ${abs(imaginary)}i"
        }
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is Complex) return false
        
        val epsilon = 1e-10
        return abs(real - other.real) < epsilon && abs(imaginary - other.imaginary) < epsilon
    }
    
    override fun hashCode(): Int {
        var result = real.hashCode()
        result = 31 * result + imaginary.hashCode()
        return result
    }
}