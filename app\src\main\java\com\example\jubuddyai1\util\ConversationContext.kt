package com.example.jubuddyai1.util

import android.content.SharedPreferences
import com.example.jubuddyai1.ChatMessage
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class ConversationContext(private val sharedPreferences: SharedPreferences) {
    private val gson = Gson()
    private val maxContextSize = 10 // Keep last 10 interactions
    private val contextKey = "conversation_context"
    
    data class Interaction(
        val userMessage: String,
        val botResponse: String,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    private var interactions = mutableListOf<Interaction>()
    
    init {
        loadContext()
    }
    
    fun addInteraction(userMessage: ChatMessage, botMessage: ChatMessage) {
        val interaction = Interaction(
            userMessage = userMessage.content,
            botResponse = botMessage.content
        )
        
        interactions.add(interaction)
        
        // Keep only the most recent interactions
        if (interactions.size > maxContextSize) {
            interactions = interactions.takeLast(maxContextSize).toMutableList()
        }
        
        saveContext()
    }
    
    fun getContextForPrompt(currentQuery: String): String {
        if (interactions.isEmpty()) {
            return "No previous context. This is the user's query: $currentQuery"
        }
        
        val contextBuilder = StringBuilder()
        contextBuilder.append("Recent conversation context:\n\n")
        
        interactions.takeLast(5).forEach { interaction ->
            contextBuilder.append("User: ${interaction.userMessage}\n")
            contextBuilder.append("Assistant: ${interaction.botResponse}\n\n")
        }
        
        contextBuilder.append("Current user query: $currentQuery")
        
        return contextBuilder.toString()
    }
    
    fun clearContext() {
        interactions.clear()
        saveContext()
    }
    
    private fun saveContext() {
        val json = gson.toJson(interactions)
        sharedPreferences.edit().putString(contextKey, json).apply()
    }
    
    private fun loadContext() {
        val json = sharedPreferences.getString(contextKey, null)
        if (json != null) {
            try {
                val type = object : TypeToken<MutableList<Interaction>>() {}.type
                interactions = gson.fromJson(json, type) ?: mutableListOf()
            } catch (e: Exception) {
                interactions = mutableListOf()
            }
        }
    }
}
