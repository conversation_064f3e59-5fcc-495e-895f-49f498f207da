package com.example.jubuddyai1

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.doOnTextChanged
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import java.util.regex.Pattern
import android.app.Dialog
import com.google.firebase.FirebaseApp
import android.app.AlertDialog
import android.widget.EditText

// Add these missing imports
import android.widget.LinearLayout
import android.view.Gravity
import android.widget.ProgressBar
import android.widget.TextView
import android.graphics.drawable.ColorDrawable
import android.graphics.Color
import android.view.WindowManager
import com.google.android.gms.auth.api.signin.GoogleSignInStatusCodes

class SignInActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "SignInActivity"
    }

    private lateinit var emailInput: EditText
    private lateinit var passwordInput: EditText
    private lateinit var signInButton: android.widget.Button
    private lateinit var createAccountText: View
    private lateinit var forgotPasswordText: TextView
    
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var googleSignInClient: GoogleSignInClient
    
    // Firebase Authentication
    private lateinit var auth: FirebaseAuth
    
    // Define a simple loading dialog instead of using ShowLoadingIndicator
    private lateinit var loadingDialog: Dialog

    private val googleSignInLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        try {
            Log.d(TAG, "Google Sign-In result received, result code: ${result.resultCode}")
            
            if (result.resultCode == RESULT_OK) {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                handleGoogleSignInResult(task)
            } else {
                hideLoading()
                // Better logging with result code
                Log.w(TAG, "Google sign-in cancelled with result code: ${result.resultCode}")
                
                // Additional diagnostics for RESULT_CANCELED (0)
                if (result.resultCode == RESULT_CANCELED) {
                    val errorData = result.data?.extras
                    if (errorData != null) {
                        for (key in errorData.keySet()) {
                            Log.d(TAG, "Intent extra - $key: ${errorData.get(key)}")
                        }
                    }
                }
                
                Toast.makeText(this, "Google sign-in was cancelled by user", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in googleSignInLauncher: ${e.message}", e)
            hideLoading()
            Toast.makeText(this, "Sign-in error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // Initialize Firebase first
            FirebaseApp.initializeApp(this)
            auth = FirebaseAuth.getInstance()
            
            // Then set content view and continue with UI initialization
            setContentView(R.layout.activity_sign_in)
            
            // Hide system UI
            hideSystemUI()
            
            // Initialize simple loading dialog
            Log.d(TAG, "Initializing loading dialog")
            initLoadingDialog()

            // Initialize Firebase Auth
            Log.d(TAG, "Initializing Firebase Auth")
            sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
            
            Log.d(TAG, "Initializing views")
            initViews()
            
            Log.d(TAG, "Setting up input validation")
            setupInputValidation()
            
            Log.d(TAG, "Setting up Google Sign-In")
            setupGoogleSignIn()
            
            Log.d(TAG, "Setting up click listeners")
            setupClickListeners()
            
            // Log authentication state for debugging
            val currentUser = auth.currentUser
            Log.d(TAG, "Current Firebase user: $currentUser")
            Log.d(TAG, "SignInActivity initialization complete")
            
            // Add to onCreate after setupGoogleSignIn()
            val lastSignedInAccount = GoogleSignIn.getLastSignedInAccount(this)
            if (lastSignedInAccount != null) {
                Log.d(TAG, "User is already signed in with Google: ${lastSignedInAccount.email}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in onCreate: ${e.message}", e)
            Toast.makeText(this, "Error initializing app: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun initLoadingDialog() {
        loadingDialog = Dialog(this).apply {
            setContentView(R.layout.loading_dialog)
            window?.apply {
                setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                setLayout(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT
                )
                setGravity(Gravity.CENTER)
            }
            setCancelable(false)
        }
    }

    private fun showLoading() {
        if (!loadingDialog.isShowing) {
            loadingDialog.show()
        }
    }

    private fun hideLoading() {
        if (loadingDialog.isShowing) {
            loadingDialog.dismiss()
        }
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }

    private fun initViews() {
        emailInput = findViewById(R.id.emailInput)
        passwordInput = findViewById(R.id.passwordInput)
        signInButton = findViewById(R.id.signInButton)
        createAccountText = findViewById(R.id.createAccountText)
        forgotPasswordText = findViewById(R.id.forgotPasswordText)
        
        // Update app title with proper spacing
        val titleText = findViewById<TextView>(R.id.titleText)
        titleText.text = "JUBuddy AI"
    }

    private fun setupInputValidation() {
        emailInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty() && !isValidEmail(s.toString())) {
                    emailInput.error = "Enter a valid email address"
                } else {
                    emailInput.error = null
                }
                updateSignInButtonState()
            }
            
            override fun afterTextChanged(s: android.text.Editable?) {}
        })
        
        passwordInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty() && s.length < 6) {
                    passwordInput.error = "Password must be at least 6 characters"
                } else {
                    passwordInput.error = null
                }
                updateSignInButtonState()
            }
            
            override fun afterTextChanged(s: android.text.Editable?) {}
        })
    }

    private fun setupGoogleSignIn() {
        try {
            // Use the client ID with client_type 3 from your google-services.json
            val webClientId = "38383343371-0rc699nkm1d0jkt8qbfpnmd16rrb6d0u.apps.googleusercontent.com"
            
            // Add debug logging to confirm the client ID is being used
            Log.d(TAG, "Using web client ID: $webClientId")
            
            // Set up sign-in options with all required scopes
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken(webClientId)
                .requestEmail()
                .requestProfile() // Request profile info too
                .build()
                
            // Initialize the client
            googleSignInClient = GoogleSignIn.getClient(this, gso)
            
            // Verify connection
            googleSignInClient.silentSignIn().addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    Log.d(TAG, "Silent sign-in check successful")
                } else {
                    Log.d(TAG, "Silent sign-in check failed: ${task.exception?.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up Google Sign-In: ${e.message}", e)
            Toast.makeText(this, "Google Sign-In setup error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupClickListeners() {
        try {
            signInButton.setOnClickListener {
                try {
                    signInWithEmailPassword()
                } catch(e: Exception) {
                    Log.e(TAG, "Error in signIn click: ${e.message}")
                    Toast.makeText(this, "Sign-in error: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }

            createAccountText.setOnClickListener {
                try {
                    startActivity(Intent(this, SignUpActivity::class.java))
                } catch(e: Exception) {
                    Log.e(TAG, "Error in createAccount click: ${e.message}")
                    Toast.makeText(this, "Account creation error: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
            
            forgotPasswordText.setOnClickListener {
                try {
                    startActivity(Intent(this, ForgotPasswordActivity::class.java))
                } catch(e: Exception) {
                    Log.e(TAG, "Error in forgotPassword click: ${e.message}")
                    Toast.makeText(this, "Error opening password reset: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up click listeners: ${e.message}")
            Toast.makeText(this, "Error in click setup: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun updateSignInButtonState() {
        val email = emailInput.text.toString()
        val password = passwordInput.text.toString()
        
        signInButton.isEnabled = email.isNotEmpty() && password.isNotEmpty() && 
                                isValidEmail(email) && password.length >= 6
    }

    private fun signInWithEmailPassword() {
        val email = emailInput.text.toString()
        val password = passwordInput.text.toString()
        
        if (!isValidEmail(email)) {
            emailInput.error = "Enter a valid email address"
            return
        }
        
        if (password.length < 6) {
            passwordInput.error = "Password must be at least 6 characters"
            return
        }
        
        showLoading()
        
        // Sign in with Firebase Auth
        auth.signInWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                hideLoading()
                if (task.isSuccessful) {
                    // Sign in success
                    Log.d(TAG, "signInWithEmail:success")
                    saveUserDataToPreferences(email, "email")
                    Toast.makeText(this, "Signed in successfully", Toast.LENGTH_SHORT).show()
                    checkTermsAndNavigate()
                } else {
                    // If sign in fails, display a message to the user
                    Log.w(TAG, "signInWithEmail:failure", task.exception)
                    
                    // Check if user doesn't exist, offer to create account
                    val errorMessage = task.exception?.message ?: ""
                    if (errorMessage.contains("no user record", ignoreCase = true) ||
                        errorMessage.contains("user not found", ignoreCase = true)) {
                        Toast.makeText(this, "Account doesn't exist. Create a new account.", 
                                      Toast.LENGTH_LONG).show()
                    } else if (errorMessage.contains("password is invalid", ignoreCase = true)) {
                        Toast.makeText(this, "Invalid password. Please try again.",
                                      Toast.LENGTH_LONG).show()
                    } else {
                        Toast.makeText(this, "Authentication failed: ${task.exception?.message}",
                                      Toast.LENGTH_LONG).show()
                    }
                }
            }
    }

    private fun createAccount() {
        val email = emailInput.text.toString()
        val password = passwordInput.text.toString()
        
        if (!isValidEmail(email)) {
            emailInput.error = "Enter a valid email address"
            return
        }
        
        if (password.length < 6) {
            passwordInput.error = "Password must be at least 6 characters"
            return
        }
        
        showLoading()
        
        // Create a new account with Firebase Auth
        auth.createUserWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                hideLoading()
                if (task.isSuccessful) {
                    // Sign up success
                    Log.d(TAG, "createUserWithEmail:success")
                    saveUserDataToPreferences(email, "email")
                    Toast.makeText(this, "Account created successfully", Toast.LENGTH_SHORT).show()
                    checkTermsAndNavigate()
                } else {
                    // If sign up fails, display a message to the user
                    Log.w(TAG, "createUserWithEmail:failure", task.exception)
                    Toast.makeText(this, "Account creation failed: ${task.exception?.message}",
                                  Toast.LENGTH_LONG).show()
                }
            }
    }

    private fun signInWithGoogle() {
        try {
            showLoading()
            
            // Check if Google Play Services is available
            val googleApiAvailability = com.google.android.gms.common.GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(this)
            
            if (resultCode != com.google.android.gms.common.ConnectionResult.SUCCESS) {
                hideLoading()
                if (googleApiAvailability.isUserResolvableError(resultCode)) {
                    googleApiAvailability.getErrorDialog(this, resultCode, 1000)?.show()
                } else {
                    Toast.makeText(this, "This device doesn't support Google Play Services", 
                                  Toast.LENGTH_LONG).show()
                }
                return
            }
            
            // Clear any existing tokens
            googleSignInClient.signOut().addOnCompleteListener {
                // Start new sign-in flow
                val signInIntent = googleSignInClient.signInIntent
                googleSignInLauncher.launch(signInIntent)
                Log.d(TAG, "Google Sign-In intent launched")
            }
        } catch (e: Exception) {
            hideLoading()
            Log.e(TAG, "Error starting Google sign-in: ${e.message}", e)
            Toast.makeText(this, "Error starting Google sign-in", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleGoogleSignInResult(completedTask: com.google.android.gms.tasks.Task<GoogleSignInAccount>) {
        try {
            val account = completedTask.getResult(ApiException::class.java)
            Log.d(TAG, "Google Sign-In successful, account ID: ${account?.id}, email: ${account?.email}")
            
            // Check if we got a valid ID token
            if (account?.idToken == null) {
                Log.e(TAG, "Google Sign-In failed: No ID token")
                Toast.makeText(this, "Google Sign-In failed: No ID token", Toast.LENGTH_SHORT).show()
                hideLoading()
                return
            }
            
            // Log token for debugging (remove in production)
            Log.d(TAG, "ID Token received: ${account.idToken!!.take(10)}...")
            
            // Create Firebase credential
            val credential = GoogleAuthProvider.getCredential(account.idToken!!, null)
            
            // Sign in with Firebase
            auth.signInWithCredential(credential)
                .addOnCompleteListener(this) { task ->
                    if (task.isSuccessful) {
                        // Sign in success
                        hideLoading()
                        Log.d(TAG, "Firebase Auth with Google: success")
                        val user = auth.currentUser
                        saveUserDataToPreferences(account.email ?: "", "google", account.displayName)
                        Toast.makeText(this, "Signed in as ${user?.email}", Toast.LENGTH_SHORT).show()
                        checkTermsAndNavigate()
                    } else {
                        // Sign in fails - try the fallback method instead of showing error
                        Log.e(TAG, "Firebase Auth with Google: failure", task.exception)
                        Log.d(TAG, "Trying fallback authentication...")
                        
                        // Try our fallback method
                        tryDirectFirebaseAuth(account)
                    }
                }
        } catch (e: ApiException) {
            hideLoading()
            // Log detailed error information
            Log.e(TAG, "Google sign in failed: code=${e.statusCode}, message=${e.message}")
            
            val errorMessage = when (e.statusCode) {
                12501 -> "Google sign-in was cancelled" // SIGN_IN_CANCELLED
                7 -> "Network error during sign-in"     // NETWORK_ERROR
                12500 -> "Google sign-in failed (${e.statusCode})" // SIGN_IN_FAILED
                else -> "Google sign-in error: ${e.statusCode}"
            }
            
            Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show()
        }
    }

    private fun saveUserDataToPreferences(email: String, authMethod: String, displayName: String? = null) {
        sharedPreferences.edit().apply {
            putString("userEmail", email)
            putString("userName", displayName ?: email.substringBefore('@'))
            putString("authMethod", authMethod)
            apply()
        }
    }

    private fun checkTermsAndNavigate() {
        val termsAccepted = sharedPreferences.getBoolean("termsAccepted", false)
        
        Log.d(TAG, "Terms already accepted: $termsAccepted")
        
        if (termsAccepted) {
            // If terms were previously accepted, go directly to main activity
            startActivity(Intent(this, MainActivity::class.java))
        } else {
            // Only show terms screen if they haven't been accepted yet
            startActivity(Intent(this, TermsActivity::class.java))
        }
        finish()
    }

    private fun isValidEmail(email: String): Boolean {
        val emailPattern = Pattern.compile(
            "[a-zA-Z0-9+._%\\-]{1,256}" +
                    "@" +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                    "(" +
                    "\\." +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                    ")+"
        )
        return emailPattern.matcher(email).matches()
    }
    
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }

    private fun tryDirectFirebaseAuth(account: GoogleSignInAccount) {
        try {
            Log.d(TAG, "Attempting direct Firebase auth with account email: ${account.email}")
            
            // Try to authenticate with Firebase directly using the account info
            // This is a fallback approach if credential auth fails
            auth.fetchSignInMethodsForEmail(account.email ?: "")
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        val methods = task.result?.signInMethods
                        Log.d(TAG, "Available sign-in methods: $methods")
                        
                        if (methods.isNullOrEmpty()) {
                            // Create a new account for this email
                            createAccountForGoogleUser(account)
                        } else {
                            // Try to sign in with Google credential one more time
                            val credential = GoogleAuthProvider.getCredential(account.idToken, null)
                            auth.signInWithCredential(credential)
                                .addOnSuccessListener {
                                    Log.d(TAG, "Retry with credential succeeded")
                                    saveUserDataToPreferences(account.email ?: "", "google", account.displayName)
                                    Toast.makeText(this, "Signed in as ${account.email}", Toast.LENGTH_SHORT).show()
                                    checkTermsAndNavigate()
                                }
                                .addOnFailureListener { e ->
                                    // Last resort - just save the account info and continue
                                    Log.e(TAG, "Retry failed, proceeding with manual user creation", e)
                                    saveUserDataToPreferences(account.email ?: "", "google", account.displayName)
                                    checkTermsAndNavigate()
                                }
                        }
                    } else {
                        Log.e(TAG, "Error checking sign-in methods", task.exception)
                        hideLoading()
                        Toast.makeText(this, "Authentication error: ${task.exception?.message}", Toast.LENGTH_SHORT).show()
                    }
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error in tryDirectFirebaseAuth: ${e.message}", e)
            hideLoading()
            Toast.makeText(this, "Authentication error: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun createAccountForGoogleUser(account: GoogleSignInAccount) {
        Log.d(TAG, "Creating new account for Google user: ${account.email}")
        
        // Get a temporary password (you can improve this for security)
        val tempPassword = "Google" + System.currentTimeMillis().toString().takeLast(6)
        
        // Create account
        auth.createUserWithEmailAndPassword(account.email ?: "", tempPassword)
            .addOnCompleteListener { task ->
                hideLoading()
                if (task.isSuccessful) {
                    Log.d(TAG, "createUserWithEmail for Google account: success")
                    saveUserDataToPreferences(account.email ?: "", "google", account.displayName)
                    Toast.makeText(this, "Account created successfully", Toast.LENGTH_SHORT).show()
                    checkTermsAndNavigate()
                } else {
                    Log.w(TAG, "createUserWithEmail for Google account: failure", task.exception)
                    Toast.makeText(this, "Account creation failed: ${task.exception?.message}", Toast.LENGTH_LONG).show()
                }
            }
    }

    private fun showForgotPasswordDialog() {
        val builder = AlertDialog.Builder(this)
        val dialogView = layoutInflater.inflate(R.layout.dialog_forgot_password, null)
        val emailInput = dialogView.findViewById<EditText>(R.id.emailInput)
        
        builder.setView(dialogView)
            .setTitle("Reset Password")
            .setPositiveButton("Send Reset Link") { dialog, _ ->
                val email = emailInput.text.toString().trim()
                if (email.isNotEmpty()) {
                    resetPassword(email)
                } else {
                    Toast.makeText(this, "Please enter your email", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
        
        builder.create().show()
    }

    private fun resetPassword(email: String) {
        FirebaseAuth.getInstance().sendPasswordResetEmail(email)
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    Toast.makeText(this, "Password reset link sent to your email", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, "Failed to send reset email: ${task.exception?.message}", Toast.LENGTH_LONG).show()
                }
            }
    }

}