<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/notesTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Notes Category"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="32dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/scienceButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="SCIENCE"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notesTitle"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="48dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/artsButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="ARTS"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scienceButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/engineeringButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="ENGINEERING"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/artsButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/backButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="BACK"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginBottom="32dp" />

    <FrameLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 