package com.example.jubuddyai1.ads

import android.app.Activity
import android.content.Context
import android.view.View
import android.widget.FrameLayout
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback

class AdManager(private val context: Context) {
    
    private var bannerAdView: AdView? = null
    private var interstitialAd: InterstitialAd? = null
    
    companion object {
        // Real banner ad unit ID
        private const val BANNER_AD_UNIT_ID = "ca-app-pub-3639460814030294/2007394818"
        // Real interstitial ad unit ID
        private const val INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3639460814030294/8010305198"
    }
    
    fun initialize() {
        MobileAds.initialize(context) {}
        loadInterstitialAd()
    }
    
    fun loadBannerAd(adContainer: FrameLayout) {
        // Create a new AdView
        val adView = AdView(context).apply {
            adUnitId = BANNER_AD_UNIT_ID
            var adSize = getAdSize(adContainer)
            this.setAdSize(adSize)
            
            adListener = object : AdListener() {
                override fun onAdLoaded() {
                    adContainer.visibility = View.VISIBLE
                }
                
                override fun onAdFailedToLoad(error: LoadAdError) {
                    adContainer.visibility = View.GONE
                }
            }
        }
        
        // Add the AdView to the container
        adContainer.removeAllViews()
        adContainer.addView(adView)
        
        // Load the ad
        val adRequest = AdRequest.Builder().build()
        adView.loadAd(adRequest)
        
        // Save reference to the banner
        bannerAdView = adView
    }
    
    private fun getAdSize(adContainer: FrameLayout): AdSize {
        val display = (context as Activity).windowManager.defaultDisplay
        val outMetrics = context.resources.displayMetrics
        
        val density = outMetrics.density
        var adWidthPixels = adContainer.width.toFloat()
        
        if (adWidthPixels == 0f) {
            adWidthPixels = outMetrics.widthPixels.toFloat()
        }
        
        val adWidth = (adWidthPixels / density).toInt()
        return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(context, adWidth)
    }
    
    /**
     * Load an interstitial ad in the background
     */
    fun loadInterstitialAd() {
        val adRequest = AdRequest.Builder().build()
        
        InterstitialAd.load(
            context, 
            INTERSTITIAL_AD_UNIT_ID, 
            adRequest,
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    interstitialAd = ad
                }
                
                override fun onAdFailedToLoad(error: LoadAdError) {
                    interstitialAd = null
                }
            }
        )
    }
    
    /**
     * Show the loaded interstitial ad if available, then execute the provided action
     */
    fun showInterstitialAd(activity: Activity, onAdDismissed: () -> Unit) {
        if (interstitialAd != null) {
            interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdDismissedFullScreenContent() {
                    // Called when ad is dismissed
                    interstitialAd = null
                    // Preload the next interstitial
                    loadInterstitialAd()
                    // Execute callback
                    onAdDismissed()
                }
                
                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    // Called when ad fails to show
                    interstitialAd = null
                    // Execute callback since we couldn't show the ad
                    onAdDismissed()
                    // Preload the next interstitial
                    loadInterstitialAd()
                }
            }
            
            interstitialAd?.show(activity)
        } else {
            // No ad available, just continue with the action
            onAdDismissed()
            // Try to load for next time
            loadInterstitialAd()
        }
    }
    
    fun destroy() {
        bannerAdView?.destroy()
        bannerAdView = null
        interstitialAd = null
    }
} 