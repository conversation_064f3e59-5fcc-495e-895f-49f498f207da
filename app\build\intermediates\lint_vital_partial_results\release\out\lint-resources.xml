http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/typing_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_camera.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/dots_3.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stop.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/dots_2.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/dots_1.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ju_logo_raw.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/typing_dots.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/loading_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_plus.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_document.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ju_logo.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_sheet_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_google.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/typing_dot.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_sign_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_terms.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_sign_up.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_forgot_password.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/typing_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/bottom_sheet_add_content.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_notes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/network_error_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/native_ad_layout.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_web_view.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_webview.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/loading_dialog.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_forgot_password.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/raw/api.properties,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:typing_animation,0,F;+color:purple_200,1,V"#FFBB86FC";black,1,V"#000000";linkColor,1,V"#2196F3";teal_200,1,V"#FF03DAC5";assistant_message_bg,1,V"#F5F5F5";white,1,V"#FFFFFF";teal_700,1,V"#FF018786";ic_launcher_background,2,V"#FFFFF9";purple_700,1,V"#FF3700B3";purple_500,1,V"#FF6200EE";user_message_bg,1,V"#E3F2FD";+dimen:message_margin_large,3,V"48dp";card_corner_radius,3,V"8dp";message_margin_small,3,V"16dp";+drawable:ic_arrow_right,4,F;ic_camera,5,F;dots_3,6,F;ic_stop,7,F;dots_2,8,F;ic_back,9,F;dots_1,10,F;ic_image,11,F;ju_logo_raw,12,F;edit_text_background,13,F;typing_dots,14,F;loading_background,15,F;ic_plus,16,F;ic_launcher_foreground,17,F;ic_document,18,F;ic_launcher_background,19,F;ju_logo,20,F;bottom_sheet_background,21,F;ic_google,22,F;typing_dot,23,F;+id:voiceButton,24,F;pinnedImagePrompt,24,F;documentAnalysisProgress,24,F;welcomeTitle,24,F;pinnedImageCard,24,F;forgotPasswordText,25,F;forgotPasswordText,25,F;homeButton,26,F;signOutButton,27,F;signOutButton,27,F;termsText,28,F;messageWebView,29,F;confirmPasswordInput,30,F;confirmPasswordInput,30,F;termsScrollView,28,F;recyclerView,24,F;titleText,25,F;titleText,25,F;titleText,30,F;titleText,30,F;titleText,28,F;titleText,28,F;typingIndicator,29,F;messageInput,24,F;pyqButton,26,F;resetButton,31,F;messageText,29,F;pinnedDocumentName,24,F;loadingText,24,F;dot3,32,F;dot2,32,F;dot1,32,F;settingsTitle,27,F;settingsTitle,27,F;cameraOption,33,F;continueButton,28,F;continueButton,28,F;imageOption,33,F;sendButton,24,F;pinnedDocumentPrompt,24,F;notesTitle,34,F;loginText,30,F;clearPinnedDocumentButton,24,F;logoImage,25,F;logoImage,25,F;logoImage,30,F;logoImage,30,F;logoImage,35,F;networkErrorOverlay,36,F;pinnedDocumentIcon,24,F;jubuddyButton,26,F;pinnedImageView,24,F;ad_headline,37,F;documentOption,33,F;retryButton,36,F;artsButton,34,F;welcomeContainer,24,F;appNameText,35,F;acceptCheckbox,28,F;acceptCheckbox,28,F;pinnedDocumentCard,24,F;ad_call_to_action,37,F;adContainer,26,F;adContainer,34,F;adContainer,27,F;syllabusButton,26,F;progressBar,38,F;progressBar,39,F;progressBar,40,F;backButton,31,F;backButton,34,F;backButton,27,F;scienceButton,34,F;ad_icon,37,F;signUpButton,30,F;signUpButton,30,F;engineeringButton,34,F;messageCard,29,F;inputLayout,24,F;imagePreview,29,F;passwordInput,25,F;passwordInput,25,F;passwordInput,30,F;passwordInput,30,F;storeButton,26,F;createAccountText,25,F;welcomeImage,24,F;welcomeSubtitle,24,F;signInButton,25,F;signInButton,25,F;emailInput,31,F;emailInput,25,F;emailInput,25,F;emailInput,30,F;emailInput,30,F;emailInput,41,F;imageAnalysisProgress,24,F;plusButton,24,F;networkErrorView,24,F;settingsButton,26,F;ad_advertiser,37,F;ad_body,37,F;webView,38,F;webView,39,F;avatarImage,29,F;pinnedContentContainer,24,F;clearPinnedImageButton,24,F;notesButton,26,F;stopGenerationButton,24,F;+layout:activity_terms,28,F;activity_forgot_password,31,F;bottom_sheet_add_content,33,F;activity_sign_in,25,F;native_ad_layout,37,F;item_message,29,F;dialog_forgot_password,41,F;activity_main,26,F;activity_notes,34,F;activity_web_view,38,F;network_error_overlay,36,F;activity_settings,27,F;typing_indicator,32,F;loading_dialog,40,F;activity_chat,24,F;activity_sign_up,30,F;activity_splash,35,F;activity_webview,39,F;+mipmap:ic_launcher_round,42,F;ic_launcher_round,43,F;ic_launcher_round,44,F;ic_launcher_round,45,F;ic_launcher_round,46,F;ic_launcher_round,47,F;ic_launcher_foreground,48,F;ic_launcher_foreground,49,F;ic_launcher_foreground,50,F;ic_launcher_foreground,51,F;ic_launcher_foreground,52,F;ic_launcher,53,F;ic_launcher,54,F;ic_launcher,55,F;ic_launcher,56,F;ic_launcher,57,F;ic_launcher,58,F;+raw:api,59,F;+string:hint_password,60,V"Password";btn_sign_in,60,V"Sign In";text_or,60,V"OR";btn_sign_in_google,60,V"Sign in with Google";error_processing_image,60,V"Error processing image\: %1$s";ask_about_text,60,V"Ask about this text...";analyzing_image,60,V"Analyzing image...";default_web_client_id,60,V"***********-0rc699nkm1d0jkt8qbfpnmd16rrb6d0u.apps.googleusercontent.com";app_name,60,V"JUBuddy AI";text_create_account,60,V"New user? Create an account";type_message,60,V"Type a message...";error_invalid_email,60,V"Enter a valid email address";error_invalid_password,60,V"Password must be at least 6 characters";analyzing_wait,60,V"Analyzing image\, please wait...";ask_about_image,60,V"Ask about this image...";hint_email,60,V"Email";+style:Theme.JUBuddyAI1,61,VDTheme.MaterialComponents.DayNight.NoActionBar,colorPrimary:@color/purple_500,colorPrimaryVariant:@android\:color/black,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,android\:statusBarColor:@android\:color/black,android\:navigationBarColor:@android\:color/black,android\:navigationBarDividerColor:@android\:color/transparent,android\:windowLightStatusBar:false,android\:windowLightNavigationBar:false,;Theme.JUBuddyAI1,62,VDTheme.MaterialComponents.DayNight.DarkActionBar,colorPrimary:@color/purple_200,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/black,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_200,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;AppTheme.NoActionBar,63,VDTheme.AppCompat.Light.NoActionBar,windowActionBar:false,windowNoTitle:true,;TransparentDialog,63,VDTheme.AppCompat.Dialog,android\:windowBackground:@android\:color/transparent,android\:windowIsFloating:true,android\:windowNoTitle:true,android\:backgroundDimEnabled:false,;+xml:network_security_config,64,F;data_extraction_rules,65,F;file_paths,66,F;backup_rules,67,F;