<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <!-- Welcome Screen -->
    <LinearLayout
        android:id="@+id/welcomeContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="#000000"
        android:visibility="visible">

        <ImageView
            android:id="@+id/welcomeImage"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ju_logo"
            android:contentDescription="JU Logo" />

        <TextView
            android:id="@+id/welcomeTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hi, I'm JUBuddy AI."
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            android:fontFamily="sans-serif-light"
            android:layout_marginTop="24dp"
            android:textAlignment="center" />

        <TextView
            android:id="@+id/welcomeSubtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="How can I help you today?"
            android:textColor="#AAAAAA"
            android:textSize="16sp"
            android:fontFamily="sans-serif-light"
            android:layout_marginTop="16dp"
            android:textAlignment="center" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/pinnedImageContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:background="#1A1A1A"
        android:padding="12dp"
        android:elevation="4dp"
        app:layout_constraintBottom_toTopOf="@id/inputLayout">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp">
            
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                
                <ImageView
                    android:id="@+id/pinnedImageView"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:scaleType="centerCrop"
                    android:adjustViewBounds="true"/>
                    
                <ProgressBar
                    android:id="@+id/imageAnalysisProgress"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:indeterminateTint="#FFFFFF"/>
            </FrameLayout>
        </androidx.cardview.widget.CardView>
        
        <ImageView
            android:id="@+id/clearPinnedImageButton"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_gravity="top|end"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="#FFFFFF"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"/>
        
        <TextView
            android:id="@+id/pinnedImagePrompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:layout_marginStart="120dp"
            android:text="Analyzing image..."
            android:textColor="#AAAAAA"
            android:textSize="16sp"/>
    </FrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="false"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="true"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/inputLayout" />

    <TextView
        android:id="@+id/loadingText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/inputLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="8dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/inputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageButton
            android:id="@+id/imageButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_gallery"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageButton
            android:id="@+id/voiceButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_btn_speak_now"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/imageButton" />

        <EditText
            android:id="@+id/messageInput"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="Type a message..."
            android:textColor="#FFFFFF"
            android:textColorHint="#808080"
            android:padding="12dp"
            android:maxLines="4"
            android:inputType="textMultiLine"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/sendButton"
            app:layout_constraintStart_toEndOf="@id/voiceButton" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/sendButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@android:drawable/ic_menu_send"
            app:fabSize="mini"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Include the network error overlay -->
    <include
        android:id="@+id/networkErrorView"
        layout="@layout/network_error_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Update the stop generation button position -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/stopGenerationButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|bottom"
        android:layout_marginBottom="20dp"
        android:contentDescription="Stop generation"
        android:visibility="gone"
        app:backgroundTint="#FF5252"
        app:elevation="6dp"
        app:fabSize="mini"
        app:layout_constraintBottom_toTopOf="@id/inputLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:src="@drawable/ic_stop" />

</androidx.constraintlayout.widget.ConstraintLayout>