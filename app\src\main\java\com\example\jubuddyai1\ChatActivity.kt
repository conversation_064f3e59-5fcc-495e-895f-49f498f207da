package com.example.jubuddyai1

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.speech.RecognizerIntent
import android.view.View
import android.widget.EditText
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import android.graphics.drawable.AnimationDrawable
import android.widget.ImageView
import android.widget.LinearLayout
import android.graphics.Color
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.os.Build
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions


import com.google.android.gms.tasks.Tasks
import android.widget.ProgressBar
import android.content.Context
import com.example.jubuddyai1.EnhancedLoadingIndicator
import java.util.Properties
import com.example.jubuddyai1.document.DocumentService
import java.util.regex.Pattern
import com.example.jubuddyai1.ads.AdManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import android.util.Log
import com.example.jubuddyai1.util.ConversationContext
import com.google.android.material.bottomsheet.BottomSheetDialog
import androidx.activity.result.contract.ActivityResultContracts
import android.provider.MediaStore
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

class ChatActivity : AppCompatActivity() {
    companion object {
        private const val STORAGE_PERMISSION_CODE = 1001
        // Declare variables here, initialize in onCreate
        lateinit var BASE_URL: String
        lateinit var API_KEY: String
        lateinit var MODEL: String
    }
    private lateinit var messageInput: EditText
    private lateinit var sendButton: FloatingActionButton
    private lateinit var voiceButton: ImageButton
    private lateinit var plusButton: ImageButton
    private lateinit var recyclerView: RecyclerView
    private lateinit var loadingText: TextView
    private lateinit var adapter: ChatAdapter
    private val messages = mutableListOf<ChatMessage>()

    private val client = OkHttpClient()

    // Activity result contracts
    private val getImageContent = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let { processImage(it) }
    }

    private val getDocumentContent = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let { processDocument(it) }
    }

    private val takePicture = registerForActivityResult(ActivityResultContracts.TakePicture()) { success: Boolean ->
        if (success) {
            cameraImageUri?.let { processImage(it) }
        }
    }

    private val speechRecognizer = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            val spokenText = result.data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)?.get(0) ?: ""
            messageInput.setText(spokenText)
        }
    }

    private var loadingAnimation: AnimationDrawable? = null
    private lateinit var loadingIndicator: EnhancedLoadingIndicator
    private lateinit var welcomeContainer: LinearLayout
    private lateinit var welcomeImage: ImageView
    private lateinit var welcomeTitle: TextView
    private lateinit var welcomeSubtitle: TextView
    
    // Flag to track if we're currently processing a response
    private var isProcessingResponse = false

    private var pinnedImageUri: Uri? = null
    private var extractedImageText: String = ""
    private var pinnedDocumentUri: Uri? = null
    private var extractedDocumentText: String = ""
    private var cameraImageUri: Uri? = null

    // Add this property
    private lateinit var documentService: DocumentService
    
    // Add this to track the latest AI response for document generation
    private var latestAiContent: String = ""
    private var pendingDocumentFormat: String? = null
    private var pendingDocumentTitle: String? = null
    private var lastGeneratedFilePath: String? = null

    // Add these at the beginning with other class properties
    private lateinit var adManager: AdManager
    private var interactionCount = 0



    private lateinit var conversationContext: ConversationContext

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set system bars to dark theme
        window.statusBarColor = Color.BLACK
        window.navigationBarColor = Color.BLACK
        
        setContentView(R.layout.activity_chat)

        // Load properties here
        try {
            val properties = Properties()
            val inputStream = resources.openRawResource(R.raw.api)
            properties.load(inputStream)
            inputStream.close()
            BASE_URL = properties.getProperty("BASE_URL")
            API_KEY = properties.getProperty("API_KEY")
            MODEL = properties.getProperty("MODEL")
        } catch (e: Exception) {
            // Handle error loading properties, e.g., show a toast or log
            Toast.makeText(this, "Error loading configuration", Toast.LENGTH_LONG).show()
            // Potentially finish the activity if configuration is critical
             finish()
             return // Stop further execution in onCreate if config fails
        }

        // Initialize document service
        documentService = DocumentService(applicationContext)

        // Initialize AdManager
        adManager = AdManager(this)
        adManager.initialize()

        // Initialize ConversationContext
        conversationContext = ConversationContext(getSharedPreferences("chat_prefs", Context.MODE_PRIVATE))

        initializeViews()
        setupRecyclerView()
        setupClickListeners()

        // Load saved messages if available
        loadChatHistory()
        
        // Set the loaded messages to adapter
        adapter.setMessages(messages)
    }

    private fun initializeViews() {
        messageInput = findViewById(R.id.messageInput)
        sendButton = findViewById(R.id.sendButton)
        voiceButton = findViewById(R.id.voiceButton)
        imageButton = findViewById(R.id.imageButton)
        recyclerView = findViewById(R.id.recyclerView)
        loadingText = findViewById(R.id.loadingText)
        loadingIndicator = EnhancedLoadingIndicator(this)
        
        // Set up the stop generation listener
        loadingIndicator.setOnStopGenerationListener(object : EnhancedLoadingIndicator.OnStopGenerationListener {
            override fun onStopGeneration() {
                // Cancel the ongoing API request
                client.dispatcher.cancelAll()
                
                // Reset processing flag
                isProcessingResponse = false
                
                // Re-enable input controls
                setInputEnabled(true)
                
                // Add a message indicating generation was stopped
                val currentAiMessageIndex = messages.size - 1
                if (currentAiMessageIndex >= 0 && !messages[currentAiMessageIndex].isUser) {
                    // Add indication that generation was stopped
                    val currentContent = messages[currentAiMessageIndex].content
                    val updatedContent = if (currentContent.isEmpty()) {
                        "[Generation stopped]"
                    } else {
                        "$currentContent\n\n[Generation stopped]"
                    }
                    
                    messages[currentAiMessageIndex] = ChatMessage(updatedContent, false)
                    adapter.updateMessage(currentAiMessageIndex, messages[currentAiMessageIndex])
                }
                
                Toast.makeText(this@ChatActivity, "Generation stopped", Toast.LENGTH_SHORT).show()
            }
        })
        
        // Initialize the loading animation
        val drawable = loadingText.compoundDrawables[2] // Get the end drawable
        if (drawable is AnimationDrawable) {
            loadingAnimation = drawable
        }

        // Initialize welcome screen views
        welcomeContainer = findViewById(R.id.welcomeContainer)
        welcomeImage = findViewById(R.id.welcomeImage)
        welcomeTitle = findViewById(R.id.welcomeTitle) 
        welcomeSubtitle = findViewById(R.id.welcomeSubtitle)
    }

    private fun setupRecyclerView() {
        adapter = ChatAdapter()
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    private fun setupClickListeners() {
        sendButton.setOnClickListener {
            val message = messageInput.text.toString().trim()
            if (message.isNotEmpty()) {
                sendMessage(message)
                messageInput.text.clear()
            }
        }

        voiceButton.setOnClickListener {
            startVoiceRecognition()
        }

        imageButton.setOnClickListener {
            checkAndRequestPermission()
        }
        
        // The stop generation button functionality is now handled by the EnhancedLoadingIndicator
    }

    private fun startVoiceRecognition() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
        }
        speechRecognizer.launch(intent)
    }

    private fun checkAndRequestPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                STORAGE_PERMISSION_CODE)
        } else {
            openImagePicker()
        }
    }

    private fun openImagePicker() {
        getContent.launch("image/*")
    }

    private fun processImage(uri: Uri) {
        try {
            // Pin the image first
            pinImage(uri)
            
            // Extract text from image in background
            extractTextFromImage(uri)
            
        } catch (e: Exception) {
            Toast.makeText(
                this,
                getString(R.string.error_processing_image, e.message),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun pinImage(uri: Uri) {
        pinnedImageUri = uri

        // Show the pinned image above the input box - using correct IDs from layout
        val pinnedContentContainer = findViewById<View>(R.id.pinnedContentContainer)
        val pinnedImageCard = findViewById<View>(R.id.pinnedImageCard)
        val pinnedImageView = findViewById<ImageView>(R.id.pinnedImageView)
        val pinnedImagePrompt = findViewById<TextView>(R.id.pinnedImagePrompt)
        val clearPinnedImageButton = findViewById<ImageView>(R.id.clearPinnedImageButton)
        val imageAnalysisProgress = findViewById<ProgressBar>(R.id.imageAnalysisProgress)

        // Show the containers with animation
        pinnedContentContainer.visibility = View.VISIBLE
        pinnedImageCard.alpha = 0f
        pinnedImageCard.visibility = View.VISIBLE
        pinnedImageCard.animate().alpha(1f).setDuration(200).start()

        // Set the image
        pinnedImageView.setImageURI(uri)

        // Show analyzing state
        imageAnalysisProgress.visibility = View.VISIBLE
        pinnedImagePrompt.text = getString(R.string.analyzing_image)

        // Set up clear button
        clearPinnedImageButton.setOnClickListener {
            pinnedImageCard.animate().alpha(0f).setDuration(200).withEndAction {
                pinnedImageCard.visibility = View.GONE
                pinnedContentContainer.visibility = View.GONE
                pinnedImageUri = null
                extractedImageText = ""
            }.start()
        }

        // Focus input field and show keyboard
        messageInput.requestFocus()
        messageInput.hint = getString(R.string.analyzing_wait)
    }

    private fun extractTextFromImage(uri: Uri) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Create bitmap for analysis
                val bitmap = when {
                    Build.VERSION.SDK_INT < 28 -> MediaStore.Images.Media.getBitmap(contentResolver, uri)
                    else -> {
                        val source = ImageDecoder.createSource(contentResolver, uri)
                        ImageDecoder.decodeBitmap(source)
                    }
                }
                
                // Use ML Kit to extract text
                val image = InputImage.fromBitmap(bitmap, 0)
                val recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
                val result = Tasks.await(recognizer.process(image))
                extractedImageText = result.text
                
                // Update UI based on detected content
                withContext(Dispatchers.Main) {
                    val pinnedImagePrompt = findViewById<TextView>(R.id.pinnedImagePrompt)
                    val imageAnalysisProgress = findViewById<ProgressBar>(R.id.imageAnalysisProgress)
                    
                    // Hide progress indicator
                    imageAnalysisProgress.visibility = View.GONE
                    messageInput.hint = getString(R.string.ask_about_image)
                    
                    // Update prompt based on content
                    if (extractedImageText.isNotEmpty()) {
                        when {
                            containsCodeIndicators(extractedImageText) -> {
                                pinnedImagePrompt.text = getString(R.string.ask_about_image)
                            }
                            containsMathIndicators(extractedImageText) -> {
                                pinnedImagePrompt.text = getString(R.string.ask_about_image)
                            }
                            containsChemistryIndicators(extractedImageText) -> {
                                pinnedImagePrompt.text = getString(R.string.ask_about_image)
                            }
                            containsPhysicsIndicators(extractedImageText) -> {
                                pinnedImagePrompt.text = getString(R.string.ask_about_image)
                            }
                            extractedImageText.length > 100 -> {
                                pinnedImagePrompt.text = getString(R.string.ask_about_text)
                            }
                            else -> {
                                pinnedImagePrompt.text = getString(R.string.ask_about_image)
                            }
                        }
                    } else {
                        pinnedImagePrompt.text = getString(R.string.ask_about_image)
                    }
                    
                    // Animate the prompt to draw attention
                    pinnedImagePrompt.alpha = 0f
                    pinnedImagePrompt.animate()
                        .alpha(1f)
                        .setDuration(300)
                        .start()
                }
            } catch (e: Exception) {
                // Default prompt if analysis fails
                withContext(Dispatchers.Main) {
                    val pinnedImagePrompt = findViewById<TextView>(R.id.pinnedImagePrompt)
                    val imageAnalysisProgress = findViewById<ProgressBar>(R.id.imageAnalysisProgress)
                    
                    imageAnalysisProgress.visibility = View.GONE
                    pinnedImagePrompt.text = getString(R.string.ask_about_image)
                    messageInput.hint = getString(R.string.ask_about_image)
                }
            }
        }
    }

    private fun containsCodeIndicators(text: String): Boolean {
        val codeKeywords = listOf(
            "function", "def ", "class ", "import ", "var ", "let ", "const ", 
            "for(", "for (", "while(", "while (", "if(", "if (", 
            "return", "#include", "public static", "int ", "void ", 
            "String[]", "print(", "console.log", "cout", "println", 
            "{\n", "}\n", ";\n"
        )
        
        return codeKeywords.any { text.contains(it) } ||
               text.contains(Regex("[{};]\\s*$")) || // Line ends with {, }, or ;
               text.contains(Regex("^\\s*[)}]")) ||   // Line starts with ) or }
               text.contains(Regex("\\w+\\(.*\\)"))   // Function calls
    }

    private fun containsMathIndicators(text: String): Boolean {
        return text.contains(Regex("[+\\-*/^=]")) &&
               text.contains(Regex("\\d+")) ||
               text.contains("√") || text.contains("∫") || 
               text.contains("lim") || text.contains("dx") ||
               text.contains("dy") || text.contains("∑") ||
               text.contains("sin") || text.contains("cos") ||
               text.contains("tan") || text.contains("log")
    }

    private fun containsChemistryIndicators(text: String): Boolean {
        return text.contains(Regex("[A-Z][a-z]?\\d*")) && // Chemical elements
               (text.contains("→") || text.contains("⇌") || 
                text.contains("H2O") || text.contains("NaCl") ||
                text.contains("mol") || text.contains("acid") ||
                text.contains("base") || text.contains("pH"))
    }

    private fun containsPhysicsIndicators(text: String): Boolean {
        return text.contains("force") || text.contains("mass") ||
               text.contains("gravity") || text.contains("velocity") ||
               text.contains("acceleration") || text.contains("momentum") ||
               text.contains("energy") || text.contains("power") ||
               text.contains("N/m") || text.contains("J/") ||
               text.contains("kg") || text.contains("m/s")
    }

    private fun detectDocumentRequest(message: String): Boolean {
        val documentPatterns = listOf(
            "(?:save|generate|create|make|export|convert)\\s+(?:this|a|an|the)?\\s*(?:as|to|into)?\\s*(?:a|an)?\\s*(pdf)\\b",
            "\\b(?:pdf)\\s+(?:file|document|version)\\b",
            "\\bsave\\s+(?:this|that|the|your|previous|last)?\\s+(?:response|answer|reply|message|text)\\b",
            "\\bcreate\\s+(?:a|an)?\\s+document\\b",
            "\\bgenerate\\s+(?:a|an)?\\s+document\\b"
        )
        
        return documentPatterns.any { Pattern.compile(it, Pattern.CASE_INSENSITIVE).matcher(message).find() }
    }
    
    private fun extractDocumentParams(message: String): Pair<String, String> {
        // Extract format
        val formatPattern = "\\b(pdf|csv|txt|text)\\b"
        val formatMatcher = Pattern.compile(formatPattern, Pattern.CASE_INSENSITIVE).matcher(message)
        val format = if (formatMatcher.find()) formatMatcher.group(1).lowercase() else "pdf"
        
        // Extract title if specified
        val titlePattern = "(?:titled|title|called|named)\\s+[\"']?([^\"']+)[\"']?"
        val titleMatcher = Pattern.compile(titlePattern, Pattern.CASE_INSENSITIVE).matcher(message)
        val title = if (titleMatcher.find()) 
            titleMatcher.group(1) 
        else 
            "JUBuddy_Document_${System.currentTimeMillis()}"
        
        return Pair(format, title)
    }
    
    private fun generateDocument(title: String, content: String, format: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            val result = documentService.generateDocument(title, content, format)
            
            withContext(Dispatchers.Main) {
                if (result.success && result.filePath != null) {
                    lastGeneratedFilePath = result.filePath
                    
                    val extension = when (format) {
                        "csv" -> "CSV"
                        "txt", "text" -> "TXT"
                        else -> "PDF"
                    }
                    
                    val successMessage = "I've created a $extension document titled \"$title\". " +
                            "Would you like to open it or share it?"
                    
                    // Add AI message
                    val aiMessage = ChatMessage(successMessage, false)
                    messages.add(aiMessage)
                    adapter.addMessage(aiMessage)
                    recyclerView.smoothScrollToPosition(messages.size - 1)
                } else {
                    val errorMessage = "I couldn't create the document. ${result.errorMessage ?: "An unknown error occurred."}"
                    
                    // Add AI message
                    val aiMessage = ChatMessage(errorMessage, false)
                    messages.add(aiMessage)
                    adapter.addMessage(aiMessage)
                }
            }
        }
    }
    
    private fun handleDocumentAction(message: String) {
        if (lastGeneratedFilePath == null) {
            val aiMessage = ChatMessage("I don't have a recently created document to open or share.", false)
            messages.add(aiMessage)
            adapter.addMessage(aiMessage)
            return
        }
        
        val openPattern = Pattern.compile("(?:open|view|show|display)\\s+(?:it|document|file)", Pattern.CASE_INSENSITIVE)
        val sharePattern = Pattern.compile("(?:share|send|email)\\s+(?:it|document|file)", Pattern.CASE_INSENSITIVE)
        
        val success = when {
            openPattern.matcher(message).find() -> documentService.openDocument(lastGeneratedFilePath!!)
            sharePattern.matcher(message).find() -> documentService.shareDocument(lastGeneratedFilePath!!)
            else -> false
        }
        
        val responseMessage = when {
            openPattern.matcher(message).find() && success -> "I've opened the document for you."
            sharePattern.matcher(message).find() && success -> "I've started sharing the document."
            openPattern.matcher(message).find() -> "I couldn't open the document. You may not have an app that can view this file type."
            sharePattern.matcher(message).find() -> "I couldn't share the document."
            else -> "Would you like to open or share the document? Just say 'open it' or 'share it'."
        }
        
        val aiMessage = ChatMessage(responseMessage, false)
        messages.add(aiMessage)
        adapter.addMessage(aiMessage)
    }

    // Modify sendMessage to handle document-related requests
    private fun sendMessage(message: String) {
        // Hide welcome screen if visible
        if (welcomeContainer.visibility == View.VISIBLE) {
            welcomeContainer.visibility = View.GONE
            recyclerView.visibility = View.VISIBLE
        }
        
        // Add user message to chat
        val chatMessage = ChatMessage(message, true, pinnedImageUri)
        messages.add(chatMessage)
        adapter.addMessage(chatMessage)
        
        // NEW: Check for agent commands first
        if (handleAgentCommands(message)) {
            return
        }
        
        // Check if this is a document request
        if (detectDocumentRequest(message)) {
            val (format, title) = extractDocumentParams(message)
            
            if (messages.size >= 2 && !messages[messages.size - 2].isUser) {
                // Use the previous AI response to generate document
                val content = messages[messages.size - 2].content
                generateDocument(title, content, format)
                return
            } else {
                // No previous AI response, set pending document and ask AI for content
                pendingDocumentFormat = format
                pendingDocumentTitle = title
                
                // Ask AI to generate content for document
                val docPrompt = "Please generate content for a $format document titled \"$title\". Be comprehensive and well-structured."
                
                // Regular API request without changing UI flow
                processApiRequest(docPrompt)
                return
            }
        }
        
        // Check if this is a document action (open/share)
        val actionPattern = Pattern.compile("(?:open|view|show|share|send)\\s+(?:it|document|file)", Pattern.CASE_INSENSITIVE)
        if (actionPattern.matcher(message).find() && lastGeneratedFilePath != null) {
            handleDocumentAction(message)
            return
        }
        
        // Regular message processing
        val isImageQuery = pinnedImageUri != null
        
        // Increment the interaction counter
        interactionCount++
        
        // Check if we should show an interstitial ad (every 2 interactions)
        if (interactionCount % 2 == 0) {
            // Show ad before continuing with the API request
            adManager.showInterstitialAd(this) {
                // Continue with the regular message processing after ad is dismissed
                processApiRequest(message)
            }
        } else {
            // Continue with regular message processing without showing an ad
            processApiRequest(message)
        }
    }
    
    // New method to separate the API request logic from the ad display logic
    private fun processApiRequest(message: String) {
        // Regular message processing
        val isImageQuery = pinnedImageUri != null
        
        // Show loading indicator with stop button
        loadingIndicator.setLoadingText("Generating response...")
        loadingIndicator.showWithStopButton(true)
        
        // Add an empty AI message that will be updated as streaming comes in
        val aiMessage = ChatMessage("", false)
        messages.add(aiMessage)
        adapter.addMessage(aiMessage)
        
        // Store the index of the AI message for later reference
        val currentAiMessageIndex = messages.size - 1
        
        // Start typing indicator animation
        val typingHandler = Handler(Looper.getMainLooper())
        val typingRunnable = object : Runnable {
            override fun run() {
                if (isProcessingResponse) {
                    // Toggle typing indicator
                    if (messages[currentAiMessageIndex].content.endsWith("▋")) {
                        adapter.hideTypingIndicator(currentAiMessageIndex)
                    } else {
                        adapter.showTypingIndicator(currentAiMessageIndex)
                    }
                    typingHandler.postDelayed(this, 300) // Blink faster for better visibility (300ms)
                }
            }
        }
        typingHandler.post(typingRunnable)
        
        // Scroll to bottom only when starting
        recyclerView.smoothScrollToPosition(messages.size - 1)

        // Get contextual prompt
        val contextualPrompt = conversationContext.getContextForPrompt(message)

        // Create the API request based on whether we have an image or not
        val requestBody = if (isImageQuery && extractedImageText.isNotEmpty()) {
            // Create a request that includes image context
            createImageContextRequest(message, extractedImageText)
        } else {
            // Regular text-only request with context
            JSONObject().apply {
                put("model", MODEL)
                put("messages", JSONArray().apply {
                    put(JSONObject().apply {
                        put("role", "system")
                        put("content", "You are JUBuddy AI, a helpful assistant that remembers conversation context. Here's the recent context and current query:\n\n$contextualPrompt")
                    })
                    put(JSONObject().apply {
                        put("role", "user")
                        put("content", message)
                    })
                })
                put("stream", true)
            }.toString()
        }

        // After creating the request, hide the pinned image
        val pinnedContentContainer = findViewById<View>(R.id.pinnedContentContainer)
        val pinnedImageCard = findViewById<View>(R.id.pinnedImageCard)
        if (pinnedImageCard.visibility == View.VISIBLE) {
            pinnedImageCard.animate().alpha(0f).setDuration(200).withEndAction {
                pinnedImageCard.visibility = View.GONE
                pinnedContentContainer.visibility = View.GONE
            }.start()
        }
        
        // Reset for next use
        pinnedImageUri = null
        extractedImageText = ""
        messageInput.hint = getString(R.string.type_message)

        // Send the API request
        sendApiRequest(requestBody)
    }

    // Extract API request logic to a separate method
    private fun sendApiRequest(requestBody: String) {
        val request = Request.Builder()
            .url("$BASE_URL/chat/completions")
            .addHeader("Authorization", "Bearer $API_KEY")
            .addHeader("Content-Type", "application/json")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .build()

        val currentAiMessageIndex = messages.size - 1
        val stringBuilder = StringBuilder()
        
        // Set flag to indicate we're processing a response
        isProcessingResponse = true
        
        // Disable input controls while generating
        setInputEnabled(false)

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    loadingIndicator.dismiss()
                    // Reset processing flag
                    isProcessingResponse = false
                    // Re-enable input controls
                    setInputEnabled(true)
                    
                    Toast.makeText(this@ChatActivity, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
                    messages.removeAt(currentAiMessageIndex)
                    adapter.notifyItemRemoved(currentAiMessageIndex)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    if (!response.isSuccessful) {
                        val errorBody = response.body?.string()
                        runOnUiThread {
                            loadingIndicator.dismiss()
                            // Reset processing flag
                            isProcessingResponse = false
                            // Re-enable input controls
                            setInputEnabled(true)
                            
                            Toast.makeText(this@ChatActivity, "Error: ${response.code} - ${errorBody ?: "No response"}", Toast.LENGTH_SHORT).show()
                            messages.removeAt(currentAiMessageIndex)
                            adapter.notifyItemRemoved(currentAiMessageIndex)
                        }
                        return
                    }
                    
                    // Process streaming response
                    response.body?.let { responseBody ->
                        val reader = responseBody.charStream().buffered()
                        var currentLine: String?
                        
                        try {
                            while (true) {
                                currentLine = reader.readLine() ?: break
                                
                                if (currentLine.isBlank() || !currentLine.startsWith("data:")) {
                                    continue
                                }
                                
                                val dataContent = currentLine.removePrefix("data:").trim()
                                if (dataContent == "[DONE]") {
                                    break
                                }
                                
                                try {
                                    val jsonObject = JSONObject(dataContent)
                                    val choices = jsonObject.optJSONArray("choices")
                                    if (choices != null && choices.length() > 0) {
                                        val choice = choices.getJSONObject(0)
                                        val delta = choice.optJSONObject("delta")
                                        val content = delta?.optString("content", "") ?: ""
                                        
                                        if (content.isNotEmpty()) {
                                            stringBuilder.append(content)
                                            
                                            runOnUiThread {
                                                // Hide typing indicator before updating content
                                                adapter.hideTypingIndicator(currentAiMessageIndex)
                                                
                                                // Use the new progressive update method for smoother streaming
                                                adapter.updateMessageProgressively(currentAiMessageIndex, stringBuilder.toString())
                                                
                                                // Store latest content for document generation
                                                latestAiContent = stringBuilder.toString()
                                                
                                                // Auto-scroll to keep up with new content
                                                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                                                val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                                if (lastVisiblePosition >= messages.size - 2) {
                                                    recyclerView.scrollToPosition(messages.size - 1)
                                                }
                                                
                                                // Add a small delay before showing typing indicator again
                                                Handler(Looper.getMainLooper()).postDelayed({
                                                    if (isProcessingResponse) {
                                                        adapter.showTypingIndicator(currentAiMessageIndex)
                                                    }
                                                }, 100)
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    // Skip malformed JSON
                                    continue
                                }
                            }
                        } catch (e: Exception) {
                            runOnUiThread {
                                loadingIndicator.dismiss()
                                // Reset processing flag
                                isProcessingResponse = false
                                // Re-enable input controls
                                setInputEnabled(true)
                                
                                Toast.makeText(this@ChatActivity, "Error reading stream: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        } finally {
                            runOnUiThread {
                                loadingIndicator.dismiss()
                                
                                // Reset processing flag
                                isProcessingResponse = false
                                
                                // Make sure typing indicator is removed at the end
                                adapter.hideTypingIndicator(currentAiMessageIndex)
                                
                                // Apply final markdown formatting once streaming is complete
                                val finalContent = stringBuilder.toString()
                                val finalMessage = ChatMessage(finalContent, false)
                                messages[currentAiMessageIndex] = finalMessage
                                
                                // Add a small delay before applying final formatting for smoother transition
                                Handler(Looper.getMainLooper()).postDelayed({
                                    adapter.updateMessage(currentAiMessageIndex, finalMessage)
                                    // Scroll to ensure the full message is visible
                                    recyclerView.smoothScrollToPosition(messages.size - 1)
                                }, 200)
                                
                                // Re-enable input controls
                                setInputEnabled(true)
                                
                                // Store this interaction in context - get the completed AI message
                                val userMessage = messages[messages.size - 2] // The user message
                                val botMessage = messages[messages.size - 1] // The completed AI message
                                
                                // Add to the conversation context
                                conversationContext.addInteraction(userMessage, botMessage)
                                
                                // Check if we need to generate a document with this response
                                if (pendingDocumentFormat != null && pendingDocumentTitle != null) {
                                    generateDocument(pendingDocumentTitle!!, latestAiContent, pendingDocumentFormat!!)
                                    pendingDocumentFormat = null
                                    pendingDocumentTitle = null
                                }
                                
                                // Add a small delay to finish any ongoing typing animations
                                Handler(Looper.getMainLooper()).postDelayed({
                                    // Scroll logic if user is near bottom
                                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                    if (messages.size - lastVisiblePosition <= 3) {
                                        recyclerView.smoothScrollToPosition(messages.size - 1)
                                    }
                                }, 300)
                            }
                        }
                    }
                    
                } catch (e: Exception) {
                    runOnUiThread {
                        loadingIndicator.dismiss()
                        // Reset processing flag
                        isProcessingResponse = false
                        // Re-enable input controls
                        setInputEnabled(true)
                        
                        Toast.makeText(this@ChatActivity, "Error parsing response: ${e.message}", Toast.LENGTH_SHORT).show()
                        messages.removeAt(currentAiMessageIndex)
                        adapter.notifyItemRemoved(currentAiMessageIndex)
                    }
                }
            }
        })
    }

    private fun createImageContextRequest(userMessage: String, extractedText: String): String {
        // Determine content type for specialized handling
        val contentType = when {
            containsCodeIndicators(extractedText) -> "code"
            containsMathIndicators(extractedText) -> "math problem"
            containsChemistryIndicators(extractedText) -> "chemistry problem"
            containsPhysicsIndicators(extractedText) -> "physics problem"
            else -> "text"
        }
        
        // Determine if the user wants an explanation or solution
        val userIntent = when {
            userMessage.contains("explain") || userMessage.contains("what") || 
            userMessage.contains("how") || userMessage.contains("why") -> 
                "explain"
            userMessage.contains("solve") || userMessage.contains("solution") || 
            userMessage.contains("calculate") || userMessage.contains("find") ->
                "solve"
            else -> "analyze" // Default intent
        }
        
        // Get conversation context
        val contextualPrompt = conversationContext.getContextForPrompt(userMessage)
        
        // Create specialized system prompts based on content type and user intent
        val systemPrompt = when (contentType) {
            "code" -> {
                when (userIntent) {
                    "explain" -> "You are a programming teacher. Consider the previous conversation context: $contextualPrompt\n\nExplain this code in detail, describing what each part does."
                    "solve" -> "You are a programming assistant. Consider the previous conversation context: $contextualPrompt\n\nSolve any issues in this code and provide an improved version."
                    else -> "You are a programming expert. Consider the previous conversation context: $contextualPrompt\n\nAnalyze this code, explain its functionality, identify any bugs, and suggest improvements."
                }
            }
            "math problem" -> {
                when (userIntent) {
                    "explain" -> "You are a math tutor. Consider the previous conversation context: $contextualPrompt\n\nExplain this math problem and the concepts involved."
                    "solve" -> "You are a math problem solver. Consider the previous conversation context: $contextualPrompt\n\nSolve this math problem step by step, showing all work clearly."
                    else -> "You are a math expert. Consider the previous conversation context: $contextualPrompt\n\nAnalyze this math problem, explain the concepts, and provide a step-by-step solution."
                }
            }
            "chemistry problem" -> {
                when (userIntent) {
                    "explain" -> "You are a chemistry teacher. Consider the previous conversation context: $contextualPrompt\n\nExplain this chemistry problem and the concepts involved."
                    "solve" -> "You are a chemistry problem solver. Consider the previous conversation context: $contextualPrompt\n\nSolve this chemistry problem step by step, using proper chemical notation."
                    else -> "You are a chemistry expert. Consider the previous conversation context: $contextualPrompt\n\nAnalyze this chemistry problem, explain the relevant concepts, and provide a detailed solution."
                }
            }
            "physics problem" -> {
                when (userIntent) {
                    "explain" -> "You are a physics teacher. Consider the previous conversation context: $contextualPrompt\n\nExplain this physics problem and the principles involved."
                    "solve" -> "You are a physics problem solver. Consider the previous conversation context: $contextualPrompt\n\nSolve this physics problem step by step, showing all formulas and calculations."
                    else -> "You are a physics expert. Consider the previous conversation context: $contextualPrompt\n\nAnalyze this physics problem, explain the relevant principles, and provide a detailed solution."
                }
            }
            else -> "You are an AI assistant that can analyze images and answer questions about them. Consider the previous conversation context: $contextualPrompt\n\nThe user has uploaded an image with text."
        }
        
        return JSONObject().apply {
            put("model", MODEL)
            put("messages", JSONArray().apply {
                // Add system message for context
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemPrompt)
                })
                
                // Add user message with extracted text as context
                put(JSONObject().apply {
                    put("role", "user")
                    put("content", """
                        I've uploaded an image containing a $contentType. Here's my question: $userMessage
                        
                        For reference, here's the text extracted from the image:
                        ```
                        $extractedText
                        ```
                        
                        Please provide a detailed, step-by-step response that directly addresses my question.
                    """.trimIndent())
                })
            })
            put("stream", true)
            // Adjust temperature based on content type
            put("temperature", if (contentType == "code" || contentType.contains("problem")) 0.3 else 0.7)
        }.toString()
    }

    // Override onSaveInstanceState to save chat history
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        
        // Save the number of messages
        outState.putInt("MESSAGES_COUNT", messages.size)
        
        // Save each message individually
        messages.forEachIndexed { index, message ->
            outState.putString("MESSAGE_CONTENT_$index", message.content)
            outState.putBoolean("MESSAGE_IS_USER_$index", message.isUser)
            message.imageUri?.let { uri ->
                outState.putString("MESSAGE_IMAGE_URI_$index", uri.toString())
            }
        }
    }
    
    // Override onRestoreInstanceState to restore chat history
    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        
        // Get the number of messages
        val messagesCount = savedInstanceState.getInt("MESSAGES_COUNT", 0)
        
        // Clear existing messages
        messages.clear()
        
        // Restore each message
        for (i in 0 until messagesCount) {
            val content = savedInstanceState.getString("MESSAGE_CONTENT_$i") ?: ""
            val isUser = savedInstanceState.getBoolean("MESSAGE_IS_USER_$i", true)
            val imageUriString = savedInstanceState.getString("MESSAGE_IMAGE_URI_$i")
            val imageUri = if (imageUriString != null) Uri.parse(imageUriString) else null
            
            // Add the restored message
            messages.add(ChatMessage(content, isUser, imageUri))
        }
        
        // Update the adapter with the restored messages
        adapter.setMessages(messages)
    }
    
    // Add this method to handle configuration changes directly
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // No need to do anything special here since we're handling state in onSaveInstanceState
    }

    // Make sure to clean up animations in onDestroy
    override fun onDestroy() {
        saveChatHistory()
        adapter.clearAnimations()
        loadingIndicator.dismiss()
        if (::adManager.isInitialized) {
            adManager.destroy()
        }
        super.onDestroy()
    }

    // Add this helper method to enable/disable input controls
    private fun setInputEnabled(enabled: Boolean) {
        messageInput.isEnabled = enabled
        sendButton.isEnabled = enabled
        imageButton.isEnabled = enabled
        voiceButton.isEnabled = enabled
        
        // Optionally show visual indication
        messageInput.alpha = if (enabled) 1.0f else 0.5f
        sendButton.alpha = if (enabled) 1.0f else 0.5f
        imageButton.alpha = if (enabled) 1.0f else 0.5f
        voiceButton.alpha = if (enabled) 1.0f else 0.5f
        
        // If disabled, show a message or indicator
        if (!enabled) {
            Toast.makeText(this, "Please wait while the bot is generating a response...", Toast.LENGTH_SHORT).show()
        }
    }

    // Add a method to handle agent-style commands
    private fun handleAgentCommands(message: String): Boolean {
        // Check if it's a document creation request
        if (detectDocumentRequest(message)) {
            val title = extractDocumentTitle(message) ?: "JUBuddy_Document_${System.currentTimeMillis()}"
            
            // Find content to save - either specified in message or use previous AI response
            val contentToSave = if (messages.size >= 2 && !messages[messages.size - 2].isUser) {
                // Use the previous AI response
                messages[messages.size - 2].content
            } else if (latestAiContent.isNotEmpty()) {
                // Use the latest AI response if available
                latestAiContent
            } else {
                // No content available
                val aiMessage = ChatMessage("I don't have any content to save as a PDF. Please provide some text or ask me a question first.", false)
                messages.add(aiMessage)
                adapter.addMessage(aiMessage)
                return true
            }
            
            // Generate PDF
            generateDocument(title, contentToSave, "pdf")
            return true
        }
        
        return false
    }

    // Extract document title from user message
    private fun extractDocumentTitle(message: String): String? {
        val titlePatterns = listOf(
            "(?:titled|title|called|named)\\s+[\"']?([^\"']+)[\"']?",
            "with\\s+(?:the)?\\s+title\\s+[\"']?([^\"']+)[\"']?",
            "name(?:d|ing)\\s+(?:it|the\\s+document)\\s+[\"']?([^\"']+)[\"']?",
            "save\\s+(?:as|with)\\s+(?:the)?\\s+title\\s+[\"']?([^\"']+)[\"']?"
        )
        
        for (pattern in titlePatterns) {
            val matcher = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE).matcher(message)
            if (matcher.find()) {
                return matcher.group(1).trim()
            }
        }
        
        return null
    }

    override fun onPause() {
        super.onPause()
        // Save current chat history
        saveChatHistory()
    }
    
    private fun saveChatHistory() {
        val prefs = getSharedPreferences("chat_prefs", Context.MODE_PRIVATE)
        val editor = prefs.edit()
        
        // Convert messages to JSON string
        val gson = Gson()
        val messagesJson = gson.toJson(messages)  // Use messages list directly
        
        // Save to SharedPreferences
        editor.putString("chat_history", messagesJson)
        editor.apply()
        
        Log.d("ChatActivity", "Saved ${messages.size} messages to SharedPreferences")
    }
    
    private fun loadChatHistory() {
        val prefs = getSharedPreferences("chat_prefs", Context.MODE_PRIVATE)
        val messagesJson = prefs.getString("chat_history", null)
        
        if (messagesJson != null) {
            try {
                val gson = Gson()
                val type = object : TypeToken<List<ChatMessage>>() {}.type
                val loadedMessages = gson.fromJson<List<ChatMessage>>(messagesJson, type)
                
                // Clear existing messages and add loaded ones
                messages.clear()
                messages.addAll(loadedMessages)
                
                // Update adapter with loaded messages
                adapter.setMessages(messages)
                
                // Hide welcome screen if we have messages
                if (messages.isNotEmpty()) {
                    welcomeContainer.visibility = View.GONE
                    recyclerView.visibility = View.VISIBLE
                }
                
                Log.d("ChatActivity", "Loaded ${messages.size} messages from SharedPreferences")
            } catch (e: Exception) {
                Log.e("ChatActivity", "Error loading chat history", e)
            }
        }
    }

    // Add these override methods to ensure persistence
    override fun onStop() {
        super.onStop()
        saveChatHistory()
    }
}
