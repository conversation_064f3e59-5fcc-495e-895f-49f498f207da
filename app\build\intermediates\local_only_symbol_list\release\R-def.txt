R_DEF: Internal format may change without notice
local
anim typing_animation
color assistant_message_bg
color black
color ic_launcher_background
color linkColor
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color user_message_bg
color white
dimen card_corner_radius
dimen message_margin_large
dimen message_margin_small
drawable bottom_sheet_background
drawable dots_1
drawable dots_2
drawable dots_3
drawable edit_text_background
drawable ic_arrow_right
drawable ic_back
drawable ic_camera
drawable ic_document
drawable ic_google
drawable ic_image
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_plus
drawable ic_stop
drawable ju_logo
drawable ju_logo_raw
drawable loading_background
drawable typing_dot
drawable typing_dots
id acceptCheckbox
id adContainer
id ad_advertiser
id ad_body
id ad_call_to_action
id ad_headline
id ad_icon
id appNameText
id artsButton
id avatarImage
id backButton
id cameraOption
id clearPinnedDocumentButton
id clearPinnedImageButton
id confirmPasswordInput
id continueButton
id createAccountText
id documentAnalysisProgress
id documentOption
id dot1
id dot2
id dot3
id emailInput
id engineeringButton
id forgotPasswordText
id homeButton
id imageAnalysisProgress
id imageOption
id imagePreview
id inputLayout
id jubuddyButton
id loadingText
id loginText
id logoImage
id messageCard
id messageInput
id messageText
id messageWebView
id networkErrorOverlay
id networkErrorView
id notesButton
id notesTitle
id passwordInput
id pinnedContentContainer
id pinnedDocumentCard
id pinnedDocumentIcon
id pinnedDocumentName
id pinnedDocumentPrompt
id pinnedImageCard
id pinnedImagePrompt
id pinnedImageView
id plusButton
id progressBar
id pyqButton
id recyclerView
id resetButton
id retryButton
id scienceButton
id sendButton
id settingsButton
id settingsTitle
id signInButton
id signOutButton
id signUpButton
id stopGenerationButton
id storeButton
id syllabusButton
id termsScrollView
id termsText
id titleText
id typingIndicator
id voiceButton
id webView
id welcomeContainer
id welcomeImage
id welcomeSubtitle
id welcomeTitle
layout activity_chat
layout activity_forgot_password
layout activity_main
layout activity_notes
layout activity_settings
layout activity_sign_in
layout activity_sign_up
layout activity_splash
layout activity_terms
layout activity_web_view
layout activity_webview
layout bottom_sheet_add_content
layout dialog_forgot_password
layout item_message
layout loading_dialog
layout native_ad_layout
layout network_error_overlay
layout typing_indicator
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
raw api
string analyzing_image
string analyzing_wait
string app_name
string ask_about_image
string ask_about_text
string btn_sign_in
string btn_sign_in_google
string default_web_client_id
string error_invalid_email
string error_invalid_password
string error_processing_image
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string hint_email
string hint_password
string project_id
string text_create_account
string text_or
string type_message
style AppTheme.NoActionBar
style Theme.JUBuddyAI1
style TransparentDialog
xml backup_rules
xml data_extraction_rules
xml file_paths
xml network_security_config
