package com.example.jubuddyai1

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import java.util.regex.Pattern

class SignUpActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "SignUpActivity"
    }

    private lateinit var emailInput: EditText
    private lateinit var passwordInput: EditText
    private lateinit var confirmPasswordInput: EditText
    private lateinit var signUpButton: Button
    private lateinit var loginText: TextView
    
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var auth: FirebaseAuth

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // Initialize Firebase
            FirebaseApp.initializeApp(this)
            auth = FirebaseAuth.getInstance()
            
            setContentView(R.layout.activity_sign_up)
            
            // Hide system UI
            hideSystemUI()
            
            sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
            
            initViews()
            setupInputValidation()
            setupClickListeners()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in onCreate: ${e.message}", e)
            Toast.makeText(this, "Error initializing app: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }

    private fun initViews() {
        emailInput = findViewById(R.id.emailInput)
        passwordInput = findViewById(R.id.passwordInput)
        confirmPasswordInput = findViewById(R.id.confirmPasswordInput)
        signUpButton = findViewById(R.id.signUpButton)
        loginText = findViewById(R.id.loginText)
    }

    private fun setupInputValidation() {
        emailInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty() && !isValidEmail(s.toString())) {
                    emailInput.error = "Enter a valid email address"
                } else {
                    emailInput.error = null
                }
                updateSignUpButtonState()
            }
            
            override fun afterTextChanged(s: android.text.Editable?) {}
        })
        
        passwordInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty() && s.length < 6) {
                    passwordInput.error = "Password must be at least 6 characters"
                } else {
                    passwordInput.error = null
                }
                updateSignUpButtonState()
            }
            
            override fun afterTextChanged(s: android.text.Editable?) {}
        })
        
        confirmPasswordInput.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val password = passwordInput.text.toString()
                if (!s.isNullOrEmpty() && s.toString() != password) {
                    confirmPasswordInput.error = "Passwords do not match"
                } else {
                    confirmPasswordInput.error = null
                }
                updateSignUpButtonState()
            }
            
            override fun afterTextChanged(s: android.text.Editable?) {}
        })
    }

    private fun setupClickListeners() {
        signUpButton.setOnClickListener {
            createAccount()
        }

        loginText.setOnClickListener {
            finish() // Return to sign in activity
        }
    }

    private fun updateSignUpButtonState() {
        val email = emailInput.text.toString()
        val password = passwordInput.text.toString()
        val confirmPassword = confirmPasswordInput.text.toString()
        
        signUpButton.isEnabled = email.isNotEmpty() && password.isNotEmpty() && confirmPassword.isNotEmpty() &&
                isValidEmail(email) && password.length >= 6 && password == confirmPassword
    }

    private fun createAccount() {
        val email = emailInput.text.toString()
        val password = passwordInput.text.toString()
        
        auth.createUserWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    // Sign up success
                    Log.d(TAG, "createUserWithEmail:success")
                    saveUserDataToPreferences(email)
                    Toast.makeText(this, "Account created successfully", Toast.LENGTH_SHORT).show()
                    startActivity(Intent(this, TermsActivity::class.java))
                    finish()
                } else {
                    // If sign up fails, display a message to the user
                    Log.w(TAG, "createUserWithEmail:failure", task.exception)
                    Toast.makeText(this, "Account creation failed: ${task.exception?.message}",
                        Toast.LENGTH_LONG).show()
                }
            }
    }

    private fun saveUserDataToPreferences(email: String) {
        sharedPreferences.edit().apply {
            putString("userEmail", email)
            putString("userName", email.substringBefore('@'))
            putString("authMethod", "email")
            apply()
        }
    }

    private fun isValidEmail(email: String): Boolean {
        val emailPattern = Pattern.compile(
            "[a-zA-Z0-9+._%\\-]{1,256}" +
                    "@" +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                    "(" +
                    "\\." +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                    ")+"
        )
        return emailPattern.matcher(email).matches()
    }
    
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }
} 