package com.example.jubuddyai1

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.TextView
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import android.net.Uri
import android.app.Dialog
import android.animation.ValueAnimator
import kotlin.math.sin
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.animation.LinearInterpolator
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.webkit.JavascriptInterface
import android.widget.Toast
import android.content.Intent
import com.example.jubuddyai1.util.MarkdownParser
import android.util.Log
import android.util.Base64

class ChatAdapter : RecyclerView.Adapter<ChatAdapter.MessageViewHolder>() {
    private val messages = mutableListOf<ChatMessage>()
    private val typingHandlers = HashMap<Int, Handler>()
    private val typingAnimators = HashMap<Int, ValueAnimator>()

    fun addMessage(message: ChatMessage) {
        messages.add(message)
        notifyItemInserted(messages.size - 1)
    }
    
    fun updateMessage(position: Int, message: ChatMessage) {
        if (position >= 0 && position < messages.size) {
            val oldMessage = messages[position]
            val oldContent = oldMessage.content
            messages[position] = message
            
            // Start typing animation if this is an AI message being updated
            if (!message.isUser && oldContent != message.content) {
                animateNewContent(position, oldContent, message.content)
            } else {
                notifyItemChanged(position)
            }
        }
    }

    // Add this method to handle typing animation
    private fun animateNewContent(position: Int, oldContent: String, newContent: String) {
        // Cancel any existing animation for this position
        typingHandlers[position]?.removeCallbacksAndMessages(null)
        typingAnimators[position]?.cancel()
        
        // Only animate if there's new content to add
        if (oldContent.length >= newContent.length) {
            notifyItemChanged(position)
            return
        }
        
        val handler = Handler(Looper.getMainLooper())
        typingHandlers[position] = handler
        
        // Determine what's new - the substring that needs to be animated
        val newTextToAdd = newContent.substring(oldContent.length)
        val charsToAnimate = newTextToAdd.length
        
        // Create a temporary message with just the old content
        val tempMessage = ChatMessage(oldContent, false, messages[position].imageUri)
        messages[position] = tempMessage
        notifyItemChanged(position)
        
        // Create animator for smooth typing
        val animator = ValueAnimator.ofInt(0, charsToAnimate)
        animator.duration = calculateDuration(charsToAnimate)
        animator.interpolator = LinearInterpolator()
        
        animator.addUpdateListener { animation ->
            val animatedChars = animation.animatedValue as Int
            if (animatedChars > 0) {
                // Update the message with progressively more characters
                val currentText = oldContent + newTextToAdd.substring(0, animatedChars)
                messages[position] = ChatMessage(currentText, false, messages[position].imageUri)
                notifyItemChanged(position)
            }
        }
        
        typingAnimators[position] = animator
        animator.start()
    }
    
    // Calculate appropriate duration based on content length
    private fun calculateDuration(charCount: Int): Long {
        // Base duration of 30ms per character, with a maximum duration
        val baseDuration = 30L * charCount  
        return minOf(baseDuration, 3000L) // Cap at 3 seconds for long texts
    }
    
    // Clean up animations when finished
    fun clearAnimations() {
        typingHandlers.forEach { (_, handler) -> 
            handler.removeCallbacksAndMessages(null)
        }
        typingAnimators.forEach { (_, animator) ->
            animator.cancel()
        }
        typingHandlers.clear()
        typingAnimators.clear()
    }
    
    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        clearAnimations()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_message, parent, false)
        return MessageViewHolder(view)
    }

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int) {
        val message = messages[position]
        holder.bind(message)
    }

    override fun getItemCount(): Int = messages.size

    // Add this method to update all messages at once
    fun setMessages(newMessages: List<ChatMessage>) {
        messages.clear()
        messages.addAll(newMessages)
        notifyDataSetChanged()
    }

    class MessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val messageText: TextView = itemView.findViewById(R.id.messageText)
        private val messageCard: CardView = itemView.findViewById(R.id.messageCard)
        private val webView: WebView = itemView.findViewById(R.id.messageWebView)
        private val avatarImage: ImageView = itemView.findViewById(R.id.avatarImage)
        private val imagePreview: ImageView = itemView.findViewById(R.id.imagePreview)
        private var typingAnimation: ValueAnimator? = null

        fun bind(message: ChatMessage) {
            // Handle image preview
            if (message.imageUri != null) {
                imagePreview.visibility = View.VISIBLE
                imagePreview.setImageURI(message.imageUri)
                
                // Add click listener to show full image
                imagePreview.setOnClickListener {
                    // Show full image in a dialog or new activity
                    showFullImage(message.imageUri)
                }
            } else {
                imagePreview.visibility = View.GONE
            }

            // Set message alignment and avatar visibility
            val params = messageCard.layoutParams as ConstraintLayout.LayoutParams
            
            if (message.isUser) {
                // User message
                params.horizontalBias = 1f  // Align to right
                avatarImage.visibility = View.GONE
            } else {
                // AI message
                params.horizontalBias = 0f  // Align to left
                avatarImage.visibility = View.VISIBLE
            }
            messageCard.layoutParams = params

            // Check for special content types with more robust detection
            val containsLatex = message.content.contains("$") || 
                              message.content.contains("\\begin{") ||
                              message.content.contains("\\end{") ||
                              message.content.contains("\\ce{") ||
                              message.content.contains("\\frac") ||
                              message.content.contains("\\sqrt")
                            
            val containsMermaid = message.content.contains("```mermaid")
            val containsPlantUML = message.content.contains("```plantuml")
            val containsOtherCodeBlock = message.content.contains("```") && 
                                        !containsMermaid && !containsPlantUML
                                        
            val containsTable = message.content.contains("| ") && 
                               message.content.contains(" |") &&
                               message.content.contains("\n|") &&
                               (message.content.contains("|-") || message.content.contains("|:"))

            // Prioritize rendering in this order: diagrams > latex/code > tables > plain markdown
            if (containsMermaid || containsPlantUML) {
                // Use WebView for diagram rendering
                messageText.visibility = View.GONE
                webView.visibility = View.VISIBLE
                
                // Reset WebView settings
                webView.settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = true
                    allowContentAccess = true
                }
                
                // Configure WebView interfaces
                webView.removeJavascriptInterface("Android")
                webView.addJavascriptInterface(WebAppInterface(itemView.context), "Android")
                
                // Set up WebView client
                webView.webViewClient = object : android.webkit.WebViewClient() {
                    override fun shouldOverrideUrlLoading(view: android.webkit.WebView, url: String): Boolean {
                        openUrlInWebView(url)
                        return true
                    }
                    
                    @android.annotation.TargetApi(android.os.Build.VERSION_CODES.N)
                    override fun shouldOverrideUrlLoading(view: android.webkit.WebView, request: android.webkit.WebResourceRequest): Boolean {
                        openUrlInWebView(request.url.toString())
                        return true
                    }
                }
                
                // Format content with enhanced rendering for diagrams
                val htmlContent = formatEnhancedContent(message.content)
                
                webView.loadDataWithBaseURL(
                    "https://jubuddyai.com/", 
                    htmlContent, 
                    "text/html", 
                    "UTF-8", 
                    null
                )
            } else if (containsLatex || containsOtherCodeBlock) {
                // Use WebView for LaTeX or code blocks
                messageText.visibility = View.GONE
                webView.visibility = View.VISIBLE
                
                // Reset WebView settings
                webView.settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                }
                
                // Configure WebView interfaces
                webView.removeJavascriptInterface("Android")
                webView.addJavascriptInterface(WebAppInterface(itemView.context), "Android")
                
                // Set up WebView client
                webView.webViewClient = object : android.webkit.WebViewClient() {
                    override fun shouldOverrideUrlLoading(view: android.webkit.WebView, url: String): Boolean {
                        openUrlInWebView(url)
                        return true
                    }
                    
                    @android.annotation.TargetApi(android.os.Build.VERSION_CODES.N)
                    override fun shouldOverrideUrlLoading(view: android.webkit.WebView, request: android.webkit.WebResourceRequest): Boolean {
                        openUrlInWebView(request.url.toString())
                        return true
                    }
                }
                
                // Format content with enhanced rendering for diagrams
                val htmlContent = formatEnhancedContent(message.content)
                
                webView.loadDataWithBaseURL(
                    "https://jubuddyai.com/", 
                    htmlContent, 
                    "text/html", 
                    "UTF-8", 
                    null
                )
            } else if (containsTable) {
                // For tables, try markdown parser first, fallback to WebView if needed
                try {
                    MarkdownParser.parseMarkdown(itemView.context, message.content, messageText)
                    
                    // Check if MarkdownParser wants us to use WebView instead
                    if (messageText.tag == "USE_WEBVIEW_FOR_DIAGRAMS") {
                        messageText.visibility = View.GONE
                        webView.visibility = View.VISIBLE
                        
                        // Reset tag after use
                        messageText.tag = null
                        
                        // Setup and load WebView
                        webView.settings.apply {
                            javaScriptEnabled = true
                            domStorageEnabled = true
                        }
                        
                        val htmlContent = formatEnhancedContent(message.content)
                        webView.loadDataWithBaseURL(
                            "https://jubuddyai.com/", 
                            htmlContent, 
                            "text/html", 
                            "UTF-8", 
                            null
                        )
                    } else {
                        messageText.visibility = View.VISIBLE
                        webView.visibility = View.GONE
                    }
                } catch (e: Exception) {
                    Log.e("ChatAdapter", "Markdown parsing failed: ${e.message}", e)
                    messageText.text = message.content
                    messageText.visibility = View.VISIBLE
                    webView.visibility = View.GONE
                }
            } else {
                // Regular text - use markdown parsing
                try {
                    MarkdownParser.parseMarkdown(itemView.context, message.content, messageText)
                } catch (e: Exception) {
                    // Fallback to plain text if markdown parsing fails
                    Log.e("ChatAdapter", "Markdown parsing failed: ${e.message}", e)
                    messageText.text = message.content
                }
                messageText.visibility = View.VISIBLE
                webView.visibility = View.GONE
                
                // Add click listener for links in text messages
                messageText.setOnClickListener { view ->
                    // Check if the clicked text contains a URL
                    val text = (view as TextView).text.toString()
                    val urlPattern = Regex("https?://[\\w\\.-]+\\.[a-z]{2,}[\\w\\./\\?=%&\\-]*")
                    val matcher = urlPattern.find(text)
                    
                    if (matcher != null) {
                        val url = matcher.value
                        openUrlInWebView(url)
                    }
                }
            }

            if (!message.isUser && message.content.isEmpty()) {
                // Show typing indicator for empty AI messages (streaming)
                messageText.visibility = View.GONE
                webView.visibility = View.GONE
                startTypingAnimation()
            } else {
                stopTypingAnimation()
            }
        }
        
        private fun formatEnhancedContent(text: String): String {
            var formatted = text
            
            // Format code blocks, including mermaid and plantuml
            val codeBlockRegex = Regex("```(.*?)\\n([\\s\\S]*?)```", RegexOption.DOT_MATCHES_ALL)
            formatted = formatted.replace(codeBlockRegex) { matchResult ->
                val language = matchResult.groupValues[1].trim()
                val code = matchResult.groupValues[2]
                    .replace("<", "&lt;")
                    .replace(">", "&gt;")
                
                when (language) {
                    "mermaid" -> formatMermaidDiagram(code)
                    "plantuml" -> formatPlantUML(code)
                    else -> formatCodeBlock(language, code)
                }
            }

            // Format tables specially
            val tableRegex = Regex("(\\|[^\\n]+\\|\\n)(\\|[-:]+[-|:]*\\|\\n)((\\|[^\\n]+\\|\\n)+)", RegexOption.MULTILINE)
            formatted = formatted.replace(tableRegex) { matchResult ->
                formatTable(matchResult.value)
                }
                
            // Format other markdown elements
            // ... existing formatting code ...
            
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
                <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
                <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
                
                <!-- Syntax highlighting -->
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
                <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
                
                <!-- Mermaid for diagrams -->
                <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
                
                <style>
                    body { 
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                        font-size: 16px; 
                        color: #FFFFFF; 
                        padding: 8px;
                        margin: 0;
                        line-height: 1.5;
                        background-color: #000000;
                    }
                    
                    /* Enhanced code styling */
                    .code-container {
                        margin: 16px 0;
                        border-radius: 8px;
                        overflow: hidden;
                        background-color: #1a1a1a;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
                    }
                    
                    .code-header {
                        background-color: #1a1a1a;
                        padding: 10px 16px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #333333;
                    }
                    
                    .language-label {
                        font-family: 'JetBrains Mono', 'Fira Code', monospace;
                        font-size: 14px;
                        color: #86b8ff;
                        font-weight: 400;
                    }
                    
                    .copy-button {
                        font-family: 'JetBrains Mono', 'Fira Code', monospace;
                        font-size: 12px;
                        color: #86b8ff;
                        cursor: pointer;
                        padding: 4px 8px;
                        border-radius: 4px;
                        transition: background-color 0.2s, color 0.2s;
                    }
                    
                    .copy-button:hover {
                        background-color: #333333;
                    }
                    
                    .code-content {
                        margin: 0;
                        padding: 16px;
                        background-color: #1a1a1a;
                        overflow-x: auto;
                    }
                    
                    code {
                        font-family: 'JetBrains Mono', 'Fira Code', monospace;
                        font-size: 14px;
                        line-height: 1.6;
                        tab-size: 4;
                        letter-spacing: -0.05em;
                    }
                    
                    /* Table styling */
                    .markdown-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 16px 0;
                        border-radius: 8px;
                        overflow: hidden;
                        font-size: 15px;
                    }
                    
                    .markdown-table th {
                        background-color: #1a1a1a;
                        color: #FFFFFF;
                        font-weight: bold;
                        text-align: left;
                        padding: 12px;
                        border: 1px solid #333;
                    }
                    
                    .markdown-table td {
                        padding: 10px 12px;
                        border: 1px solid #333;
                        color: #d4d4d4;
                    }
                    
                    .markdown-table tr:nth-child(even) {
                        background-color: rgba(255, 255, 255, 0.05);
                    }
                    
                    /* Diagram container */
                    .diagram-container {
                        background-color: #1a1a1a;
                        border-radius: 8px;
                        padding: 16px;
                        margin: 16px 0;
                        overflow-x: auto;
                    }
                    
                    /* Mermaid diagram customization */
                    .mermaid {
                        background-color: #1a1a1a;
                        padding: 16px;
                        border-radius: 8px;
                    }
                    
                    /* Syntax highlighting colors */
                    .hljs-comment { color: #6a9955; }
                    .hljs-keyword { color: #569cd6; }
                    .hljs-built_in { color: #4ec9b0; }
                    .hljs-string { color: #ce9178; }
                    .hljs-number { color: #b5cea8; }
                    .hljs-function { color: #dcdcaa; }
                    .hljs-title { color: #dcdcaa; }
                    .hljs-params { color: #9cdcfe; }
                    .hljs-variable { color: #9cdcfe; }
                    .hljs-operator { color: #d4d4d4; }
                </style>
                
                <script>
                    // Configure mermaid
                    mermaid.initialize({
                        theme: 'dark',
                        securityLevel: 'loose',
                        startOnLoad: true
                    });
                    
                    // Simple utility to get the code text from a code block
                    function getCodeFromButton(button) {
                        const codeBlock = button.parentNode.nextElementSibling;
                        return codeBlock.textContent;
                    }
                    
                    // Copy function that calls Android
                    function copyToClipboard(button) {
                        const code = getCodeFromButton(button);
                        
                        try {
                            // Call the Android interface
                            Android.copyToClipboard(code);
                            
                            // Update button appearance
                            button.textContent = 'Copied!';
                            button.style.color = '#4ec9b0';
                            
                            // Reset after 2 seconds
                            setTimeout(function() {
                                button.textContent = 'Copy code';
                                button.style.color = '#86b8ff';
                            }, 2000);
                        } catch (e) {
                            console.error('Copy failed:', e);
                            alert('Copy failed: ' + e.message);
                        }
                    }
                    
                    // Run after DOM is loaded
                    document.addEventListener('DOMContentLoaded', function() {
                        // Render mermaid diagrams
                        mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                        
                        // Highlight all code blocks
                        document.querySelectorAll('pre code').forEach(function(block) {
                            hljs.highlightBlock(block);
                        });
                        
                        // Add click events to all copy buttons
                        document.querySelectorAll('.copy-button').forEach(function(button) {
                            button.addEventListener('click', function() {
                                copyToClipboard(this);
                            });
                        });
                        
                        // Handle link clicks
                        document.addEventListener('click', function(e) {
                            var target = e.target;
                            
                            while(target && target.tagName !== 'A') {
                                target = target.parentElement;
                            }
                            
                            if (target && target.tagName === 'A') {
                                e.preventDefault();
                                var url = target.getAttribute('href');
                                if (url) {
                                    Android.openLink(url);
                                }
                            }
                        });
                    });
                </script>
            </head>
            <body>
                $formatted
            </body>
            </html>
            """
        }
        
        private fun formatCodeBlock(language: String, code: String): String {
            return """
            <div class="code-container">
                <div class="code-header">
                    <span class="language-label">$language</span>
                    <span class="copy-button" onclick="copyToClipboard(this)">Copy code</span>
                </div>
                <pre class="code-content"><code class="language-$language">$code</code></pre>
            </div>
            """
        }
        
        private fun formatMermaidDiagram(code: String): String {
            return """
            <div class="diagram-container">
                <div class="code-header">
                    <span class="language-label">mermaid</span>
                    <span class="copy-button" onclick="copyToClipboard(this)">Copy code</span>
                </div>
                <div class="mermaid">
                    $code
                </div>
            </div>
            """
        }
        
        private fun formatPlantUML(code: String): String {
            try {
                // Use Android's built-in Base64 encoder
                val compressed = compress(code)
                val encoded = android.util.Base64.encodeToString(compressed, android.util.Base64.URL_SAFE)
                val cleanEncoded = encoded.replace("=", "")
                val imageUrl = "https://www.plantuml.com/plantuml/svg/$cleanEncoded"
                
                return """
                <div class="diagram-container">
                    <div class="code-header">
                        <span class="language-label">plantuml</span>
                        <span class="copy-button" onclick="copyToClipboard(this)">Copy code</span>
                    </div>
                    <div style="text-align: center; padding: 16px;">
                        <img src="$imageUrl" alt="PlantUML Diagram" style="max-width: 100%;">
                    </div>
                    <pre class="code-content" style="display: none;"><code>$code</code></pre>
                </div>
                """
            } catch (e: Exception) {
                Log.e("ChatAdapter", "Error encoding PlantUML: ${e.message}", e)
                // Fallback to just showing the code
                return formatCodeBlock("plantuml", code)
            }
        }
        
        // Add this helper method for PlantUML encoding
        private fun compress(source: String): ByteArray {
            try {
                val bais = java.io.ByteArrayInputStream(source.toByteArray(Charsets.UTF_8))
                val baos = java.io.ByteArrayOutputStream()
                val deflater = java.util.zip.Deflater()
                deflater.setLevel(java.util.zip.Deflater.BEST_COMPRESSION)
                
                val buffer = ByteArray(1024)
                val deflaterOutputStream = java.util.zip.DeflaterOutputStream(baos, deflater)
                
                var length: Int
                while (bais.read(buffer).also { length = it } > 0) {
                    deflaterOutputStream.write(buffer, 0, length)
                }
                
                bais.close()
                deflaterOutputStream.finish()
                deflaterOutputStream.close()
                
                return baos.toByteArray()
            } catch (e: Exception) {
                Log.e("ChatAdapter", "Compression error: ${e.message}", e)
                // If compression fails, return the original string as bytes
                return source.toByteArray(Charsets.UTF_8)
            }
        }
        
        private fun formatTable(tableText: String): String {
            try {
                val rows = tableText.trim().split("\n")
                if (rows.size < 3) return tableText // Need at least header, separator, and one row
                
                val headerRow = rows[0]
                val separatorRow = rows[1]
                
                // Parse headers
                val headers = headerRow.split("|")
                    .filter { it.isNotEmpty() }
                    .map { it.trim() }
                
                // Parse alignments from separator row
                val alignments = separatorRow.split("|")
                    .filter { it.isNotEmpty() }
                    .map { cell ->
                        val trimmed = cell.trim()
                        when {
                            trimmed.startsWith(":") && trimmed.endsWith(":") -> "center"
                            trimmed.endsWith(":") -> "right"
                            else -> "left"
                        }
                    }
                
                // Build HTML table
                val sb = StringBuilder()
                sb.append("<table class=\"markdown-table\">")
                
                // Table header
                sb.append("<thead><tr>")
                headers.forEachIndexed { index, header ->
                    val align = if (index < alignments.size) "text-align: ${alignments[index]};" else ""
                    sb.append("<th style=\"$align\">$header</th>")
                }
                sb.append("</tr></thead>")
                
                // Table body
                sb.append("<tbody>")
                for (i in 2 until rows.size) {
                    val cells = rows[i].split("|")
                        .filter { it.isNotEmpty() }
                        .map { it.trim() }
                    
                    sb.append("<tr>")
                    cells.forEachIndexed { index, cell ->
                        val align = if (index < alignments.size) "text-align: ${alignments[index]};" else ""
                        sb.append("<td style=\"$align\">$cell</td>")
                    }
                    sb.append("</tr>")
                }
                sb.append("</tbody></table>")
                
                return sb.toString()
            } catch (e: Exception) {
                Log.e("ChatAdapter", "Error formatting table: ${e.message}", e)
                return tableText
            }
        }

        private fun showFullImage(uri: Uri) {
            val dialog = Dialog(itemView.context, android.R.style.Theme_Black_NoTitleBar_Fullscreen)
            val imageView = ImageView(itemView.context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                scaleType = ImageView.ScaleType.FIT_CENTER
                setImageURI(uri)
                
                // Add click listener to dismiss
                setOnClickListener {
                    dialog.dismiss()
                }
            }
            dialog.setContentView(imageView)
            dialog.show()
        }

        private fun startTypingAnimation() {
            val dot1 = itemView.findViewById<View>(R.id.dot1)
            val dot2 = itemView.findViewById<View>(R.id.dot2)
            val dot3 = itemView.findViewById<View>(R.id.dot3)

            typingAnimation = ValueAnimator.ofFloat(0f, 1f).apply {
                duration = 1000
                repeatCount = ValueAnimator.INFINITE
                repeatMode = ValueAnimator.RESTART
                
                addUpdateListener { animator ->
                    val progress = animator.animatedValue as Float
                    
                    // Fix using Kotlin's Math.PI constant instead of android.util.Math
                    dot1.alpha = (sin(progress * Math.PI.toFloat() * 2f) + 1f) / 2f
                    dot2.alpha = (sin((progress + 0.2f) * Math.PI.toFloat() * 2f) + 1f) / 2f
                    dot3.alpha = (sin((progress + 0.4f) * Math.PI.toFloat() * 2f) + 1f) / 2f
                }
                
                start()
            }
        }

        private fun stopTypingAnimation() {
            typingAnimation?.cancel()
            typingAnimation = null
        }

        private fun animateTextAppearance(newText: String) {
            val oldText = messageText.text.toString()
            if (newText.length > oldText.length) {
                // Only animate new characters
                val diff = newText.substring(oldText.length)
                val animator = ValueAnimator.ofInt(0, diff.length)
                animator.duration = diff.length * 20L // 20ms per character
                
                animator.addUpdateListener { animation ->
                    val progress = animation.animatedValue as Int
                    messageText.text = oldText + diff.substring(0, progress)
                }
                
                animator.start()
            } else {
                messageText.text = newText
            }
        }

        private fun formatCodeBlocks(text: String): String {
            var formatted = text
            
            // Format code blocks with enhanced styling
            val codeBlockRegex = Regex("```(.*?)\\n([\\s\\S]*?)```", RegexOption.DOT_MATCHES_ALL)
            formatted = formatted.replace(codeBlockRegex) { matchResult ->
                val language = matchResult.groupValues[1].trim()
                val code = matchResult.groupValues[2]
                    .replace("<", "&lt;")
                    .replace(">", "&gt;")
                
                // Create an elegant code block with syntax highlighting
                """
                <div class="code-container">
                    <div class="code-header">
                        <span class="language-label">$language</span>
                    </div>
                    <pre><code class="language-$language">$code</code></pre>
                </div>
                """
            }
            
            // Enhance inline code styling
            val inlineCodeRegex = Regex("`([^`]+)`")
            formatted = formatted.replace(inlineCodeRegex) { matchResult ->
                val code = matchResult.groupValues[1]
                    .replace("<", "&lt;")
                    .replace(">", "&gt;")
                "<code class=\"inline-code\">$code</code>"
            }
            
            return formatted
        }

        private fun openUrlInWebView(url: String) {
            // Start the WebViewActivity with the URL
            val context = itemView.context
            val intent = Intent(context, WebViewActivity::class.java).apply {
                putExtra("url", url)
            }
            context.startActivity(intent)
        }

        private inner class WebAppInterface(private val context: Context) {
            @JavascriptInterface
            fun copyToClipboard(text: String) {
                try {
                    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val clip = ClipData.newPlainText("Code", text)
                    clipboard.setPrimaryClip(clip)
                    
                    // Show toast notification
                    Handler(Looper.getMainLooper()).post {
                        Toast.makeText(context, "Code copied to clipboard", Toast.LENGTH_SHORT).show()
                    }
                } catch (e: Exception) {
                    // Handle any clipboard errors
                    Handler(Looper.getMainLooper()).post {
                        Toast.makeText(context, "Failed to copy: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }

            @JavascriptInterface
            fun openLink(url: String) {
                Handler(Looper.getMainLooper()).post {
                    val intent = Intent(context, WebViewActivity::class.java).apply {
                        putExtra("url", url)
                    }
                    context.startActivity(intent)
                }
            }
        }
    }
}
