package com.example.jubuddyai1

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Button

class EnhancedLoadingIndicator(private val context: Context) {
    private val loadingDialog: Dialog = Dialog(context)
    private var onStopGenerationListener: OnStopGenerationListener? = null
    private lateinit var loadingText: TextView
    private lateinit var stopButton: Button
    
    interface OnStopGenerationListener {
        fun onStopGeneration()
    }
    
    init {
        val layout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setPadding(40, 30, 40, 30)
            
            // Add progress bar
            addView(ProgressBar(context))
            
            // Add text view
            loadingText = TextView(context).apply {
                text = "Loading..."
                setTextColor(Color.WHITE)
                setPadding(20, 20, 20, 10)
                textAlignment = TextView.TEXT_ALIGNMENT_CENTER
            }
            addView(loadingText)
            
            // Add stop button
            stopButton = Button(context).apply {
                text = "Stop"
                setBackgroundColor(Color.parseColor("#FF5252"))
                setTextColor(Color.WHITE)
                setPadding(20, 10, 20, 10)
                visibility = android.view.View.GONE
                setOnClickListener {
                    onStopGenerationListener?.onStopGeneration()
                    dismiss()
                }
            }
            addView(stopButton)
        }
        
        loadingDialog.apply {
            setContentView(layout)
            window?.apply {
                setBackgroundDrawable(ColorDrawable(Color.parseColor("#80000000")))
                attributes = attributes.apply {
                    width = WindowManager.LayoutParams.WRAP_CONTENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                    gravity = Gravity.CENTER
                }
            }
            setCancelable(false)
        }
    }
    
    fun setLoadingText(text: String) {
        loadingText.text = text
    }
    
    fun showWithStopButton(showStop: Boolean) {
        stopButton.visibility = if (showStop) android.view.View.VISIBLE else android.view.View.GONE
        show()
    }
    
    fun show() {
        if (!loadingDialog.isShowing) {
            loadingDialog.show()
        }
    }
    
    fun dismiss() {
        if (loadingDialog.isShowing) {
            loadingDialog.dismiss()
        }
    }
    
    fun setOnStopGenerationListener(listener: OnStopGenerationListener) {
        onStopGenerationListener = listener
    }
}
