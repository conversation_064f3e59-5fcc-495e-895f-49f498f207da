<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Terms and Conditions"
        android:textSize="22sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="16dp" />

    <ScrollView
        android:id="@+id/termsScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:background="@android:color/white"
        android:padding="8dp"
        app:layout_constraintBottom_toTopOf="@+id/acceptCheckbox"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleText">

        <TextView
            android:id="@+id/termsText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="4dp"
            android:padding="8dp"
            android:textSize="16sp" />
    </ScrollView>

    <CheckBox
        android:id="@+id/acceptCheckbox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="I have read and accept the Terms and Conditions"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/continueButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/continueButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Continue"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp" />

</androidx.constraintlayout.widget.ConstraintLayout> 