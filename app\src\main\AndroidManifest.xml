<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Add internet permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    
    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.JUBuddyAI1"
        android:usesCleartextTraffic="true"
        tools:targetApi="31"
        android:requestLegacyExternalStorage="true"
        android:networkSecurityConfig="@xml/network_security_config">
        
        <!-- Add inside the application tag, before the activities -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3639460814030294~3347511713"/>
        
        <!-- Make SplashActivity the launcher activity -->
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".SignInActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        
        <activity
            android:name=".SignUpActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        
        <activity
            android:name=".TermsActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
            
        <activity
            android:name=".MainActivity"
            android:exported="false">
        </activity>
        
        <activity
            android:name=".ChatActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true">
        </activity>
        
        <activity android:name=".WebViewActivity"
            android:exported="false" />
            
        <activity
            android:name=".NotesActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false" />
            
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
            
        <activity
            android:name=".ForgotPasswordActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
            
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>