<variant
    name="release"
    package="com.example.jubuddyai1"
    minSdkVersion="24"
    targetSdkVersion="33"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.2.0;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\classes;build\tmp\kotlin-classes\release;build\kotlinToolingMetadata;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      type="MAIN"
      applicationId="com.example.jubuddyai1"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\724e122ecba579f66e7825448d83ba1e\transformed\desugar_jdk_libs_configuration-1.1.5-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d7204a82bb9b66c6aa1ea144dca8f1c\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
