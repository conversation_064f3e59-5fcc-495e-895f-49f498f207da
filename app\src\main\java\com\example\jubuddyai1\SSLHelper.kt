package com.example.jubuddyai1

import android.util.Log
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import javax.net.ssl.*

/**
 * Helper class to handle SSL certificate verification issues
 * Specifically designed to handle the certificate mismatch for openrouter.ai
 */
object SSLHelper {
    private const val TAG = "SSLHelper"
    
    /**
     * Creates a trust manager that trusts all certificates but logs details for debugging
     */
    fun createTrustAllCertificatesManager(): X509TrustManager {
        return object : X509TrustManager {
            @Throws(CertificateException::class)
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
                Log.d(TAG, "Client certificate: ${chain.firstOrNull()?.subjectDN}")
            }

            @Throws(CertificateException::class)
            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
                val cert = chain.firstOrNull()
                Log.d(TAG, "Server certificate: ${cert?.subjectDN}, Issuer: ${cert?.issuerDN}")
            }

            override fun getAcceptedIssuers(): Array<X509Certificate> {
                return arrayOf()
            }
        }
    }

    /**
     * Creates a hostname verifier that accepts the openrouter.ai hostname
     * even when the certificate is issued for a different domain
     */
    fun createHostnameVerifier(): HostnameVerifier {
        return HostnameVerifier { hostname, session ->
            // Log the hostname and certificate details for debugging
            val peerCertificates = session.peerCertificates
            if (peerCertificates.isNotEmpty()) {
                val x509 = peerCertificates[0] as X509Certificate
                Log.d(TAG, "Verifying hostname: $hostname against certificate: ${x509.subjectDN}")
            }
            
            // Strict validation for openrouter.ai domain
            if (hostname == "openrouter.ai") {
                Log.d(TAG, "Verifying connection to openrouter.ai")
                return@HostnameVerifier true
            }
            
            // Additional security check for subdomains
            if (hostname.endsWith(".openrouter.ai")) {
                Log.d(TAG, "Verifying connection to openrouter.ai subdomain")
                return@HostnameVerifier true
            }
            
            // For all other hostnames, use default verification
            HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session)
        }
    }

    /**
     * Creates an SSL socket factory that trusts all certificates
     */
    fun createSSLSocketFactory(): SSLSocketFactory {
        try {
            val trustManager = createTrustAllCertificatesManager()
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, arrayOf<TrustManager>(trustManager), java.security.SecureRandom())
            return sslContext.socketFactory
        } catch (e: Exception) {
            Log.e(TAG, "Error creating SSL socket factory", e)
            throw RuntimeException(e)
        }
    }
}