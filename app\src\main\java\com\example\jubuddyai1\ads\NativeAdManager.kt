package com.example.jubuddyai1.ads

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.example.jubuddyai1.R
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdView

class NativeAdManager(private val context: Context) {
    private var currentNativeAd: NativeAd? = null
    
    fun loadNativeAd(adContainerView: View, adUnitId: String) {
        val adLoader = AdLoader.Builder(context, adUnitId)
            .forNativeAd { nativeAd ->
                // If this callback occurs after the activity is destroyed, you must call
                // destroy and return or you may get a memory leak
                if ((context as? android.app.Activity)?.isDestroyed == true) {
                    nativeAd.destroy()
                    return@forNativeAd
                }
                
                // Show the ad
                currentNativeAd?.destroy()
                currentNativeAd = nativeAd
                
                val adView = LayoutInflater.from(context)
                    .inflate(R.layout.native_ad_layout, null) as NativeAdView
                
                populateNativeAdView(nativeAd, adView)
                
                // Remove any existing ad views
                if (adContainerView is android.view.ViewGroup) {
                    adContainerView.removeAllViews()
                    adContainerView.addView(adView)
                }
            }
            .withAdListener(object : AdListener() {
                override fun onAdFailedToLoad(error: LoadAdError) {
                    // Handle ad loading failure
                    adContainerView.visibility = View.GONE
                }
                
                override fun onAdLoaded() {
                    adContainerView.visibility = View.VISIBLE
                }
            })
            .build()
        
        adLoader.loadAd(com.google.android.gms.ads.AdRequest.Builder().build())
    }
    
    private fun populateNativeAdView(nativeAd: NativeAd, adView: NativeAdView) {
        // Set the headline
        adView.findViewById<TextView>(R.id.ad_headline)?.let {
            it.text = nativeAd.headline
            adView.headlineView = it
        }
        
        // Set the body
        adView.findViewById<TextView>(R.id.ad_body)?.let {
            it.text = nativeAd.body
            it.visibility = if (nativeAd.body == null) View.INVISIBLE else View.VISIBLE
            adView.bodyView = it
        }
        
        // Set the call to action
        adView.findViewById<Button>(R.id.ad_call_to_action)?.let {
            it.text = nativeAd.callToAction
            it.visibility = if (nativeAd.callToAction == null) View.INVISIBLE else View.VISIBLE
            adView.callToActionView = it
        }
        
        // Set the icon
        adView.findViewById<ImageView>(R.id.ad_icon)?.let {
            if (nativeAd.icon == null) {
                it.visibility = View.GONE
            } else {
                it.setImageDrawable(nativeAd.icon?.drawable)
                it.visibility = View.VISIBLE
            }
            adView.iconView = it
        }
        
        // Set the advertiser name
        adView.findViewById<TextView>(R.id.ad_advertiser)?.let {
            it.text = nativeAd.advertiser
            it.visibility = if (nativeAd.advertiser == null) View.INVISIBLE else View.VISIBLE
            adView.advertiserView = it
        }
        
        // Associate the NativeAdView with the NativeAd
        adView.setNativeAd(nativeAd)
    }
    
    fun destroy() {
        currentNativeAd?.destroy()
    }
} 