package com.example.jubuddyai1.document

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.widget.Toast
import androidx.core.content.FileProvider
import com.itextpdf.text.Document
import com.itextpdf.text.Font
import com.itextpdf.text.Paragraph
import com.itextpdf.text.pdf.PdfWriter
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

data class DocumentResult(
    val success: Boolean,
    val filePath: String? = null,
    val errorMessage: String? = null
)

class DocumentService(private val context: Context) {
    companion object {
        private const val TAG = "DocumentService"
    }

    fun generateDocument(title: String, content: String, format: String): DocumentResult {
        return try {
            val fileName = sanitizeFileName("${title}_${getCurrentTimestamp()}")
            val filePath = when (format.lowercase()) {
                "pdf" -> generatePdf(fileName, content)
                "txt", "text" -> generateTextFile(fileName, content)
                "csv" -> generateCsvFile(fileName, content)
                else -> generatePdf(fileName, content) // Default to PDF
            }
            
            DocumentResult(true, filePath = filePath)
        } catch (e: Exception) {
            DocumentResult(false, errorMessage = e.message)
        }
    }
    
    private fun generatePdf(fileName: String, content: String): String {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)
        val pdfFile = File(storageDir, "$fileName.pdf")
        
        // Create PDF document
        val document = Document()
        PdfWriter.getInstance(document, FileOutputStream(pdfFile))
        document.open()
        
        // Add title
        val titleFont = Font(Font.FontFamily.HELVETICA, 18f, Font.BOLD)
        document.add(Paragraph(fileName, titleFont))
        document.add(Paragraph(" ")) // Add space after title
        
        // Add content with regular font
        val contentFont = Font(Font.FontFamily.HELVETICA, 12f)
        document.add(Paragraph(content, contentFont))
        
        // Close document
        document.close()
        
        return pdfFile.absolutePath
    }
    
    private fun generateTextFile(fileName: String, content: String): String {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)
        val textFile = File(storageDir, "$fileName.txt")
        
        // Write content to text file
        val writer = OutputStreamWriter(FileOutputStream(textFile))
        writer.write(content)
        writer.close()
        
        return textFile.absolutePath
    }
    
    private fun generateCsvFile(fileName: String, content: String): String {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)
        val csvFile = File(storageDir, "$fileName.csv")
        
        // Write content to CSV file
        val writer = OutputStreamWriter(FileOutputStream(csvFile))
        writer.write(content)
        writer.close()
        
        return csvFile.absolutePath
    }
    
    fun openDocument(filePath: String): Boolean {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                return false
            }
            
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
            
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, getMimeType(filePath))
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            context.startActivity(Intent.createChooser(intent, "Open Document"))
            return true
        } catch (e: Exception) {
            Toast.makeText(context, "Unable to open document: ${e.message}", Toast.LENGTH_SHORT).show()
            return false
        }
    }
    
    fun shareDocument(filePath: String): Boolean {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                return false
            }
            
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
            
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, uri)
                type = getMimeType(filePath)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
            context.startActivity(Intent.createChooser(shareIntent, "Share Document"))
            return true
        } catch (e: Exception) {
            Toast.makeText(context, "Unable to share document: ${e.message}", Toast.LENGTH_SHORT).show()
            return false
        }
    }
    
    private fun getMimeType(filePath: String): String {
        return when {
            filePath.endsWith(".pdf", ignoreCase = true) -> "application/pdf"
            filePath.endsWith(".txt", ignoreCase = true) -> "text/plain"
            filePath.endsWith(".csv", ignoreCase = true) -> "text/csv"
            else -> "*/*"
        }
    }
    
    private fun getCurrentTimestamp(): String {
        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
        return dateFormat.format(Date())
    }
    
    private fun sanitizeFileName(fileName: String): String {
        return fileName.replace("[\\\\/:*?\"<>|]".toRegex(), "_")
    }
} 