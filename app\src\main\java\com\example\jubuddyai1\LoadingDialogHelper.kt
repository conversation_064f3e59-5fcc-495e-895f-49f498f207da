package com.example.jubuddyai1

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView

class LoadingDialogHelper(private val context: Context) {
    private val loadingDialog: Dialog = Dialog(context)
    
    init {
        val layout = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
            setPadding(40, 30, 40, 30)
            
            // Add progress bar
            addView(ProgressBar(context))
            
            // Add text view
            addView(TextView(context).apply {
                text = "Loading..."
                setTextColor(Color.WHITE)
                setPadding(20, 0, 0, 0)
            })
        }
        
        loadingDialog.apply {
            setContentView(layout)
            window?.apply {
                setBackgroundDrawable(ColorDrawable(Color.parseColor("#80000000")))
                attributes = attributes.apply {
                    width = WindowManager.LayoutParams.WRAP_CONTENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                    gravity = Gravity.CENTER
                }
            }
            setCancelable(false)
        }
    }
    
    fun show() {
        if (!loadingDialog.isShowing) {
            loadingDialog.show()
        }
    }
    
    fun dismiss() {
        if (loadingDialog.isShowing) {
            loadingDialog.dismiss()
        }
    }
} 