package com.example.jubuddyai1.util

import android.content.Context
import android.graphics.Typeface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.*
import android.text.util.Linkify
import android.view.View
import android.widget.TextView
import java.util.regex.Pattern
import android.util.Log
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Looper
import java.util.concurrent.Executors

class MarkdownParser {
    companion object {
        private const val TAG = "MarkdownParser"
        
        // Regular expressions for Markdown syntax
        private val PATTERN_HEADING = Pattern.compile("^(#{1,6})\\s+(.+)$", Pattern.MULTILINE)
        private val PATTERN_BOLD = Pattern.compile("\\*\\*(.+?)\\*\\*")
        private val PATTERN_ITALIC = Pattern.compile("\\*(.+?)\\*")
        private val PATTERN_CODE_BLOCK = Pattern.compile("```([a-zA-Z]*)\\n([\\s\\S]*?)```", Pattern.MULTILINE)
        private val PATTERN_INLINE_CODE = Pattern.compile("`([^`]+?)`")
        private val PATTERN_LINK = Pattern.compile("\\[(.+?)\\]\\((.+?)\\)")
        private val PATTERN_BULLET_LIST = Pattern.compile("^\\s*[-*+]\\s+(.+)$", Pattern.MULTILINE)
        private val PATTERN_NUMBERED_LIST = Pattern.compile("^\\s*(\\d+)\\.\\s+(.+)$", Pattern.MULTILINE)
        private val PATTERN_BLOCKQUOTE = Pattern.compile("^>\\s+(.+)$", Pattern.MULTILINE)
        private val PATTERN_HORIZONTAL_RULE = Pattern.compile("^(?:---|\\*\\*\\*|___)$", Pattern.MULTILINE)
        
        // New patterns for tables and diagrams
        private val PATTERN_TABLE = Pattern.compile("(\\|[^\\n]+\\|\\n)(\\|[-:]+[-|:]*\\|\\n)((\\|[^\\n]+\\|\\n)+)", Pattern.MULTILINE)
        private val PATTERN_MERMAID = Pattern.compile("```mermaid\\n([\\s\\S]*?)```", Pattern.MULTILINE)
        private val PATTERN_PLANTUML = Pattern.compile("```plantuml\\n([\\s\\S]*?)```", Pattern.MULTILINE)

        // Executor for background processing
        private val executor = Executors.newSingleThreadExecutor()
        private val mainHandler = Handler(Looper.getMainLooper())

        /**
         * Parse Markdown text and apply formatting to a TextView
         */
        fun parseMarkdown(context: Context, markdownText: String, textView: TextView) {
            try {
                if (markdownText.isEmpty()) {
                    textView.text = ""
                    return
                }
                
                val spannableBuilder = SpannableStringBuilder(markdownText)
                
                // Check for diagram content
                val containsDiagram = 
                    markdownText.contains("```mermaid") || 
                    markdownText.contains("```plantuml")
                
                // If it contains diagrams, WebView rendering is more appropriate
                if (containsDiagram) {
                    Log.d(TAG, "Diagram detected, using WebView renderer")
                    renderInWebView(context, markdownText, textView)
                    return
                }
                
                // Apply individual formatters with safeguards
                safeApply { applyCodeBlocks(spannableBuilder, context) }
                safeApply { applyHeadings(spannableBuilder) }
                safeApply { applyBold(spannableBuilder) }
                safeApply { applyItalic(spannableBuilder) }
                safeApply { applyTables(spannableBuilder, context) }
                safeApply { applyInlineCode(spannableBuilder, context) }
                safeApply { applyLinks(spannableBuilder) }
                safeApply { applyLists(spannableBuilder) }
                safeApply { applyBlockquotes(spannableBuilder, context) }
                safeApply { applyHorizontalRules(spannableBuilder) }
                
                // Set the formatted text to the TextView
                textView.text = spannableBuilder
                
                // Enable auto-linking
                try {
                    Linkify.addLinks(textView, Linkify.WEB_URLS)
                } catch (e: Exception) {
                    Log.e(TAG, "Error applying auto-links: ${e.message}", e)
                }
            } catch (e: Exception) {
                // If anything goes wrong, fall back to plain text
                Log.e(TAG, "Error parsing markdown: ${e.message}", e)
                textView.text = markdownText
            }
        }
        
        private fun renderInWebView(context: Context, markdownText: String, textView: TextView) {
            // Signal to the adapter that this content should be displayed in WebView
            textView.tag = "USE_WEBVIEW_FOR_DIAGRAMS"
            textView.text = markdownText
        }
        
        private inline fun safeApply(action: () -> Unit) {
            try {
                action()
            } catch (e: Exception) {
                Log.e(TAG, "Error in markdown formatting: ${e.message}", e)
                // Continue with other formatters even if one fails
            }
        }

        private fun applyTables(spannable: SpannableStringBuilder, context: Context) {
            val matcher = PATTERN_TABLE.matcher(spannable)
            val replacements = mutableListOf<Triple<Int, Int, String>>() // Start, end, replacement
            
            while (matcher.find()) {
                val start = matcher.start()
                val end = matcher.end()
                
                if (start >= 0 && end <= spannable.length) {
                    // Extract table parts
                    val headerRow = matcher.group(1) ?: ""
                    val separatorRow = matcher.group(2) ?: ""
                    val bodyRows = matcher.group(3) ?: ""
                    
                    // Format the table
                    val formattedTable = formatTableHtml(headerRow, separatorRow, bodyRows)
                    replacements.add(Triple(start, end, formattedTable))
                }
            }
            
            // Apply replacements from end to start to avoid index shifting
            replacements.sortByDescending { it.first }
            replacements.forEach { (start, end, replacement) ->
                if (start >= 0 && end <= spannable.length) {
                    spannable.replace(start, end, replacement)
                }
            }
        }
        
        private fun formatTableHtml(headerRow: String, separatorRow: String, bodyRows: String): String {
            try {
                // Parse header alignment from separator row
                val alignments = parseSeparatorRowForAlignment(separatorRow)
                
                // Parse header cells
                val headerCells = parseTableRow(headerRow)
                
                // Parse body rows
                val bodyRowsList = bodyRows.trim().split("\n").filter { it.isNotEmpty() }
                val parsedBodyRows = bodyRowsList.map { parseTableRow(it) }
                
                // Format into a better plain text representation with unicode box drawing chars
                val sb = StringBuilder("\n")
                
                // Calculate column widths based on content
                val colWidths = calculateColumnWidths(headerCells, parsedBodyRows)
                
                // Top border
                sb.append("┌")
                colWidths.forEach { width -> sb.append("─".repeat(width + 2)).append("┬") }
                sb.deleteCharAt(sb.length - 1).append("┐\n")
                
                // Header content
                sb.append("│ ")
                headerCells.forEachIndexed { index, cell ->
                    val width = if (index < colWidths.size) colWidths[index] else 10
                    val cellContent = cell.take(width).padEnd(width)
                    sb.append(cellContent).append(" │ ")
                }
                sb.deleteCharAt(sb.length - 1).append("\n")
                
                // Separator
                sb.append("├")
                colWidths.forEach { width -> sb.append("─".repeat(width + 2)).append("┼") }
                sb.deleteCharAt(sb.length - 1).append("┤\n")
                
                // Body rows
                parsedBodyRows.forEach { rowCells ->
                    sb.append("│ ")
                    rowCells.forEachIndexed { index, cell -> 
                        val width = if (index < colWidths.size) colWidths[index] else 10
                        val cellContent = cell.take(width).padEnd(width)
                        sb.append(cellContent).append(" │ ") 
                    }
                    sb.deleteCharAt(sb.length - 1).append("\n")
                }
                
                // Bottom border
                sb.append("└")
                colWidths.forEach { width -> sb.append("─".repeat(width + 2)).append("┴") }
                sb.deleteCharAt(sb.length - 1).append("┘\n")
                
                return sb.toString()
            } catch (e: Exception) {
                Log.e(TAG, "Error formatting table: ${e.message}", e)
                // Return original content if formatting fails
                return "$headerRow$separatorRow$bodyRows"
            }
        }
        
        private fun parseSeparatorRowForAlignment(separatorRow: String): List<String> {
            val cells = separatorRow.trim().split("|").filter { it.isNotEmpty() }
            return cells.map { cell ->
                val trimmed = cell.trim()
                when {
                    trimmed.startsWith(":") && trimmed.endsWith(":") -> "center"
                    trimmed.endsWith(":") -> "right"
                    else -> "left"
                }
            }
        }
        
        private fun parseTableRow(row: String): List<String> {
            return row.trim().split("|").filter { it.isNotEmpty() }.map { it.trim() }
        }

        private fun applyHeadings(spannable: SpannableStringBuilder) {
            val matcher = PATTERN_HEADING.matcher(spannable)
            while (matcher.find()) {
                val level = matcher.group(1)?.length ?: 0
                val size = when {
                    level >= 4 -> 1.1f
                    level == 3 -> 1.2f
                    level == 2 -> 1.3f
                    else -> 1.4f
                }
                
                // Ensure we don't exceed string bounds
                val start = matcher.start(2).coerceAtMost(spannable.length - 1).coerceAtLeast(0)
                val end = matcher.end(2).coerceAtMost(spannable.length).coerceAtLeast(start)
                
                if (start < end) {
                    spannable.setSpan(
                        StyleSpan(Typeface.BOLD),
                        start,
                        end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    spannable.setSpan(
                        RelativeSizeSpan(size),
                        start,
                        end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
        }

        private fun applyBold(spannable: SpannableStringBuilder) {
            val matcher = PATTERN_BOLD.matcher(spannable)
            val replacements = mutableListOf<Triple<Int, Int, Int>>() // Start, end, length adjustment
            
            while (matcher.find()) {
                val fullStart = matcher.start(0)
                val fullEnd = matcher.end(0)
                val contentStart = matcher.start(1)
                val contentEnd = matcher.end(1)
                
                // Check bounds before applying spans
                if (contentStart < contentEnd && contentStart >= 0 && contentEnd <= spannable.length) {
                    spannable.setSpan(
                        StyleSpan(Typeface.BOLD),
                        contentStart,
                        contentEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Store replacements to be applied afterward (from end to start)
                    replacements.add(Triple(fullEnd - 2, fullEnd, -2)) // Remove trailing **
                    replacements.add(Triple(fullStart, fullStart + 2, -2)) // Remove leading **
                }
            }
            
            // Apply replacements from end to start to avoid index shifting
            replacements.sortByDescending { it.first }
            replacements.forEach { (start, end, _) ->
                if (start >= 0 && end <= spannable.length && start < end) {
                    spannable.replace(start, end, "")
                }
            }
        }

        private fun applyItalic(spannable: SpannableStringBuilder) {
            val matcher = PATTERN_ITALIC.matcher(spannable)
            val replacements = mutableListOf<Triple<Int, Int, Int>>() // Start, end, length adjustment
            
            while (matcher.find()) {
                val fullStart = matcher.start(0)
                val fullEnd = matcher.end(0)
                val contentStart = matcher.start(1)
                val contentEnd = matcher.end(1)
                
                // Check bounds before applying spans
                if (contentStart < contentEnd && contentStart >= 0 && contentEnd <= spannable.length) {
                    spannable.setSpan(
                        StyleSpan(Typeface.ITALIC),
                        contentStart,
                        contentEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Store replacements to be applied afterward (from end to start)
                    replacements.add(Triple(fullEnd - 1, fullEnd, -1)) // Remove trailing *
                    replacements.add(Triple(fullStart, fullStart + 1, -1)) // Remove leading *
                }
            }
            
            // Apply replacements from end to start to avoid index shifting
            replacements.sortByDescending { it.first }
            replacements.forEach { (start, end, _) ->
                if (start >= 0 && end <= spannable.length && start < end) {
                    spannable.replace(start, end, "")
                }
            }
        }

        private fun applyCodeBlocks(spannable: SpannableStringBuilder, context: Context) {
            val matcher = PATTERN_CODE_BLOCK.matcher(spannable)
            while (matcher.find()) {
                val fullStart = matcher.start(0)
                val fullEnd = matcher.end(0)
                val codeStart = matcher.start(2)
                val codeEnd = matcher.end(2)
                val language = matcher.group(1) ?: ""
                
                // Ensure indices are valid
                if (fullStart < 0 || fullEnd > spannable.length || codeStart < 0 || codeEnd > spannable.length) {
                    continue
                }
                
                // Apply monospace typeface to code
                if (codeStart < codeEnd) {
                    spannable.setSpan(
                        TypefaceSpan("monospace"),
                        codeStart,
                        codeEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                
                // Apply background to entire block
                if (fullStart < fullEnd) {
                    spannable.setSpan(
                        BackgroundColorSpan(0xFFEEEEEE.toInt()),
                        fullStart,
                        fullEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Add language label if provided and there's space
                    if (language.isNotEmpty() && codeStart > fullStart) {
                        try {
                            spannable.insert(codeStart, "$language\n")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error inserting language label: ${e.message}", e)
                        }
                    }
                }
            }
        }

        private fun applyInlineCode(spannable: SpannableStringBuilder, context: Context) {
            val matcher = PATTERN_INLINE_CODE.matcher(spannable)
            val replacements = mutableListOf<Triple<Int, Int, Int>>() // Start, end, length adjustment
            
            while (matcher.find()) {
                val fullStart = matcher.start(0)
                val fullEnd = matcher.end(0)
                val contentStart = matcher.start(1)
                val contentEnd = matcher.end(1)
                
                // Check bounds before applying spans
                if (contentStart < contentEnd && fullStart >= 0 && fullEnd <= spannable.length) {
                    // Apply monospace font
                    spannable.setSpan(
                        TypefaceSpan("monospace"),
                        contentStart,
                        contentEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Apply background
                    spannable.setSpan(
                        BackgroundColorSpan(0xFFEEEEEE.toInt()),
                        fullStart,
                        fullEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Store replacements to be applied afterward (from end to start)
                    replacements.add(Triple(fullEnd - 1, fullEnd, -1)) // Remove trailing `
                    replacements.add(Triple(fullStart, fullStart + 1, -1)) // Remove leading `
                }
            }
            
            // Apply replacements from end to start to avoid index shifting
            replacements.sortByDescending { it.first }
            replacements.forEach { (start, end, _) ->
                if (start >= 0 && end <= spannable.length && start < end) {
                    spannable.replace(start, end, "")
                }
            }
        }

        private fun applyLinks(spannable: SpannableStringBuilder) {
            val matcher = PATTERN_LINK.matcher(spannable)
            val replacements = mutableListOf<Triple<Int, Int, String>>() // Start, end, replacement
            
            while (matcher.find()) {
                val fullStart = matcher.start(0)
                val fullEnd = matcher.end(0)
                val textStart = matcher.start(1)
                val textEnd = matcher.end(1)
                val url = matcher.group(2) ?: ""
                
                // Check bounds before applying spans
                if (textStart < textEnd && fullStart >= 0 && fullEnd <= spannable.length) {
                    // Set URL span on the text portion
                    spannable.setSpan(
                        URLSpan(url),
                        textStart,
                        textEnd,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Get the text that should remain
                    val linkText = spannable.subSequence(textStart, textEnd).toString()
                    
                    // Store the full replacement
                    replacements.add(Triple(fullStart, fullEnd, linkText))
                }
            }
            
            // Apply replacements from end to start to avoid index shifting
            replacements.sortByDescending { it.first }
            replacements.forEach { (start, end, replacement) ->
                if (start >= 0 && end <= spannable.length && start < end) {
                    spannable.replace(start, end, replacement)
                }
            }
        }

        private fun applyLists(spannable: SpannableStringBuilder) {
            // Bullet lists
            val bulletMatcher = PATTERN_BULLET_LIST.matcher(spannable)
            val bulletReplacements = mutableListOf<Triple<Int, Int, String>>()
            
            while (bulletMatcher.find()) {
                val start = bulletMatcher.start(0)
                val itemStart = bulletMatcher.start(1)
                
                // Only replace the prefix, not the content
                if (start >= 0 && itemStart > start && start < spannable.length) {
                    bulletReplacements.add(Triple(start, itemStart, "• "))
                }
            }
            
            // Apply bullet replacements from end to start
            bulletReplacements.sortByDescending { it.first }
            bulletReplacements.forEach { (start, end, replacement) ->
                if (start >= 0 && end <= spannable.length && start < end) {
                    spannable.replace(start, end, replacement)
                }
            }
            
            // Numbered lists
            val numberedMatcher = PATTERN_NUMBERED_LIST.matcher(spannable)
            val numberedReplacements = mutableListOf<Triple<Int, Int, String>>()
            
            while (numberedMatcher.find()) {
                val start = numberedMatcher.start(0)
                val numberEnd = numberedMatcher.end(1)
                val itemStart = numberedMatcher.start(2)
                val number = numberedMatcher.group(1) ?: "1"
                
                // Only replace the prefix, not the content
                if (start >= 0 && itemStart > start && start < spannable.length) {
                    numberedReplacements.add(Triple(start, itemStart, "$number. "))
                }
            }
            
            // Apply numbered replacements from end to start
            numberedReplacements.sortByDescending { it.first }
            numberedReplacements.forEach { (start, end, replacement) ->
                if (start >= 0 && end <= spannable.length && start < end) {
                    spannable.replace(start, end, replacement)
                }
            }
        }

        private fun applyBlockquotes(spannable: SpannableStringBuilder, context: Context) {
            val matcher = PATTERN_BLOCKQUOTE.matcher(spannable)
            while (matcher.find()) {
                val start = matcher.start(0)
                val end = matcher.end(0)
                val contentStart = matcher.start(1)
                val contentEnd = matcher.end(1)
                
                // Check bounds before applying spans
                if (start < end && start >= 0 && end <= spannable.length) {
                    // Apply quote span to the whole line
                    spannable.setSpan(
                        QuoteSpan(0xFF808080.toInt()),
                        start,
                        end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    
                    // Apply italic to content if indices are valid
                    if (contentStart < contentEnd && contentStart >= 0 && contentEnd <= spannable.length) {
                        spannable.setSpan(
                            StyleSpan(Typeface.ITALIC),
                            contentStart,
                            contentEnd,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    
                    // Replace the ">" prefix with proper indentation if indices are valid
                    if (start < contentStart && start >= 0 && contentStart <= spannable.length) {
                        spannable.replace(start, contentStart, "  ")
                    }
                }
            }
        }

        private fun applyHorizontalRules(spannable: SpannableStringBuilder) {
            val matcher = PATTERN_HORIZONTAL_RULE.matcher(spannable)
            val replacements = mutableListOf<Pair<Int, Int>>()
            
            while (matcher.find()) {
                val start = matcher.start(0)
                val end = matcher.end(0)
                
                // Check bounds before replacing
                if (start < end && start >= 0 && end <= spannable.length) {
                    replacements.add(Pair(start, end))
                }
            }
            
            // Apply replacements from end to start to avoid index shifting
            replacements.sortByDescending { it.first }
            replacements.forEach { (start, end) ->
                if (start >= 0 && end <= spannable.length) {
                    spannable.replace(start, end, "\n————————————————————\n")
                }
            }
        }

        // Add this helper method to calculate optimal column widths
        private fun calculateColumnWidths(headerCells: List<String>, bodyRows: List<List<String>>): List<Int> {
            val maxCols = headerCells.size
            val colWidths = MutableList(maxCols) { 0 }
            
            // Consider header widths
            headerCells.forEachIndexed { index, cell ->
                if (index < maxCols) {
                    colWidths[index] = cell.length.coerceAtLeast(colWidths[index])
                }
            }
            
            // Consider row content widths
            bodyRows.forEach { row ->
                row.forEachIndexed { index, cell ->
                    if (index < maxCols) {
                        colWidths[index] = cell.length.coerceAtLeast(colWidths[index])
                    }
                }
            }
            
            // Cap widths at a reasonable maximum and ensure minimum width
            return colWidths.map { width -> width.coerceIn(3, 15) }
        }
    }
} 