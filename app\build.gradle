plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.example.jubuddyai1'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.jubuddyai1"
        minSdk 24
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        viewBinding = false
    }

    sourceSets {
        main {
            java {
                exclude '**/ShowLoadingIndicator.kt'
            }
        }
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
        
        // Add these new exclusions to fix the errors
        exclude 'META-INF/AL2.0' 
        exclude 'META-INF/LGPL2.1'
        exclude 'META-INF/proguard/**'
        exclude 'META-INF/versions/**'
        exclude 'org/apache/poi/util/POILogger.class'
        exclude 'org/apache/logging/**'
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // ML Kit dependencies - fixed the spacing issues
    implementation 'com.google.mlkit:text-recognition:16.0.0'
    implementation 'com.google.mlkit:object-detection:17.0.0'
    implementation 'com.google.mlkit:object-detection-custom:17.0.0'
    implementation 'com.google.mlkit:image-labeling:17.0.7'
    
    // OkHttp for network requests
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // Google Play Services Tasks dependency
    implementation 'com.google.android.gms:play-services-tasks:18.0.2'
    
    // Google Sign-In dependency
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    
    // Add Firebase dependencies
    implementation platform('com.google.firebase:firebase-bom:33.0.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    
    implementation 'com.firebaseui:firebase-ui-auth:8.0.2'
    
    // For PDF generation
    implementation 'com.itextpdf:itextg:5.5.10'
    
    // Add desugaring support
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
    
    // Add Google AdMob
    implementation 'com.google.android.gms:play-services-ads:22.6.0'
    
    // Add Gson library
    implementation 'com.google.code.gson:gson:2.10.1'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}

tasks.register('cleanDuplicateClasses', Delete) {
    delete fileTree("${buildDir}/tmp/kotlin-classes") {
        include '**/ShowLoadingIndicator.class'
    }
    delete fileTree("${buildDir}/intermediates/javac") {
        include '**/ShowLoadingIndicator.class'
    }
}

tasks.whenTaskAdded { task ->
    if (task.name.contains('compile')) {
        task.dependsOn cleanDuplicateClasses
    }
} 