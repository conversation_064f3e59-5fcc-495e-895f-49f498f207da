<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res"><file name="typing_animation" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\anim\typing_animation.xml" qualifiers="" type="anim"/><file name="dots_1" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\dots_1.xml" qualifiers="" type="drawable"/><file name="dots_2" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\dots_2.xml" qualifiers="" type="drawable"/><file name="dots_3" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\dots_3.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_google" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_google.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="ju_logo" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ju_logo.xml" qualifiers="" type="drawable"/><file name="ju_logo_raw" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ju_logo_raw.png" qualifiers="" type="drawable"/><file name="loading_background" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\loading_background.xml" qualifiers="" type="drawable"/><file name="typing_dot" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\typing_dot.xml" qualifiers="" type="drawable"/><file name="typing_dots" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\typing_dots.xml" qualifiers="" type="drawable"/><file name="activity_chat" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_chat.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_notes" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_notes.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_sign_in" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_sign_in.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_terms" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_terms.xml" qualifiers="" type="layout"/><file name="activity_webview" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_webview.xml" qualifiers="" type="layout"/><file name="activity_web_view" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_web_view.xml" qualifiers="" type="layout"/><file name="item_message" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\item_message.xml" qualifiers="" type="layout"/><file name="loading_dialog" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\loading_dialog.xml" qualifiers="" type="layout"/><file name="network_error_overlay" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\network_error_overlay.xml" qualifiers="" type="layout"/><file name="typing_indicator" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\typing_indicator.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#000000</color><color name="white">#FFFFFF</color><color name="user_message_bg">#E3F2FD</color><color name="assistant_message_bg">#F5F5F5</color><color name="linkColor">#2196F3</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="card_corner_radius">8dp</dimen><dimen name="message_margin_large">48dp</dimen><dimen name="message_margin_small">16dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">JUBuddy AI</string><string name="hint_email">Email</string><string name="hint_password">Password</string><string name="btn_sign_in">Sign In</string><string name="btn_sign_in_google">Sign in with Google</string><string name="text_create_account">New user? Create an account</string><string name="text_or">OR</string><string name="error_invalid_email">Enter a valid email address</string><string name="error_invalid_password">Password must be at least 6 characters</string><string name="default_web_client_id" translatable="false">***********-0rc699nkm1d0jkt8qbfpnmd16rrb6d0u.apps.googleusercontent.com</string><string name="analyzing_wait">Analyzing image, please wait...</string><string name="type_message">Type a message...</string><string name="analyzing_image">Analyzing image...</string><string name="error_processing_image">Error processing image: %1$s</string><string name="ask_about_image">Ask about this image...</string><string name="ask_about_text">Ask about this text...</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values\styles.xml" qualifiers=""><style name="TransparentDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style><style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.JUBuddyAI1" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@android:color/black</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@android:color/black</item>
        
        <item name="android:navigationBarColor">@android:color/black</item>
        
        <item name="android:navigationBarDividerColor">@android:color/transparent</item>
        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.JUBuddyAI1" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="api" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\raw\api.properties" qualifiers="" type="raw"/><file name="activity_sign_up" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_sign_up.xml" qualifiers="" type="layout"/><file name="dialog_forgot_password" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\dialog_forgot_password.xml" qualifiers="" type="layout"/><file name="activity_forgot_password" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\activity_forgot_password.xml" qualifiers="" type="layout"/><file name="ic_back" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="native_ad_layout" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\native_ad_layout.xml" qualifiers="" type="layout"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFF9</color></file><file name="bottom_sheet_background" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_document" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_document.xml" qualifiers="" type="drawable"/><file name="ic_image" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_plus" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\drawable\ic_plus.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_add_content" path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\main\res\layout\bottom_sheet_add_content.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\build\generated\res\processReleaseGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\JUBuddyAI1\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">***********-0rc699nkm1d0jkt8qbfpnmd16rrb6d0u.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">***********</string><string name="google_api_key" translatable="false">AIzaSyB45IgHu_AfABtuN5lD_jI-1DPgH639sgg</string><string name="google_app_id" translatable="false">1:***********:android:cbaeec9e2987552d29b40e</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyB45IgHu_AfABtuN5lD_jI-1DPgH639sgg</string><string name="google_storage_bucket" translatable="false">jubuddy-ai.firebasestorage.app</string><string name="project_id" translatable="false">jubuddy-ai</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>