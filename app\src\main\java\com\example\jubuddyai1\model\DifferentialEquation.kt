package com.example.jubuddyai1.model

import kotlin.math.*

/**
 * Advanced differential equation solver for handling ODEs and PDEs.
 * Supports various numerical methods for solving differential equations
 * commonly encountered in physics and engineering applications.
 */
class DifferentialEquation {
    
    /**
     * Represents a first-order ordinary differential equation of the form dy/dx = f(x, y)
     */
    class FirstOrderODE(val function: (Double, Double) -> Double) {
        /**
         * Solves the ODE using the Runge-Kutta 4th order method
         * @param x0 Initial x value
         * @param y0 Initial y value
         * @param xEnd End x value
         * @param stepSize Step size for numerical integration
         * @return Pair of x and y arrays containing the solution points
         */
        fun solveRungeKutta4(x0: Double, y0: Double, xEnd: Double, stepSize: Double): Pair<DoubleArray, DoubleArray> {
            val steps = ceil((xEnd - x0) / stepSize).toInt()
            val xValues = DoubleArray(steps + 1)
            val yValues = DoubleArray(steps + 1)
            
            xValues[0] = x0
            yValues[0] = y0
            
            var x = x0
            var y = y0
            
            for (i in 1..steps) {
                val k1 = function(x, y)
                val k2 = function(x + stepSize/2, y + stepSize/2 * k1)
                val k3 = function(x + stepSize/2, y + stepSize/2 * k2)
                val k4 = function(x + stepSize, y + stepSize * k3)
                
                y += stepSize * (k1 + 2*k2 + 2*k3 + k4) / 6
                x += stepSize
                
                xValues[i] = x
                yValues[i] = y
            }
            
            return Pair(xValues, yValues)
        }
        
        /**
         * Solves the ODE using the Euler method (simpler but less accurate)
         * @param x0 Initial x value
         * @param y0 Initial y value
         * @param xEnd End x value
         * @param stepSize Step size for numerical integration
         * @return Pair of x and y arrays containing the solution points
         */
        fun solveEuler(x0: Double, y0: Double, xEnd: Double, stepSize: Double): Pair<DoubleArray, DoubleArray> {
            val steps = ceil((xEnd - x0) / stepSize).toInt()
            val xValues = DoubleArray(steps + 1)
            val yValues = DoubleArray(steps + 1)
            
            xValues[0] = x0
            yValues[0] = y0
            
            var x = x0
            var y = y0
            
            for (i in 1..steps) {
                y += stepSize * function(x, y)
                x += stepSize
                
                xValues[i] = x
                yValues[i] = y
            }
            
            return Pair(xValues, yValues)
        }
    }
    
    /**
     * Represents a system of first-order ODEs: dy/dx = f(x, y) where y and f are vectors
     */
    class SystemOfODEs(val functions: List<(Double, DoubleArray) -> Double>) {
        val dimension = functions.size
        
        /**
         * Solves the system using the Runge-Kutta 4th order method
         * @param x0 Initial x value
         * @param y0 Initial y values (vector)
         * @param xEnd End x value
         * @param stepSize Step size for numerical integration
         * @return Pair of x array and list of y arrays containing the solution points
         */
        fun solveRungeKutta4(x0: Double, y0: DoubleArray, xEnd: Double, stepSize: Double): Pair<DoubleArray, List<DoubleArray>> {
            require(y0.size == dimension) { "Initial values array must match the system dimension" }
            
            val steps = ceil((xEnd - x0) / stepSize).toInt()
            val xValues = DoubleArray(steps + 1)
            val yValues = List(dimension) { DoubleArray(steps + 1) }
            
            xValues[0] = x0
            for (j in 0 until dimension) {
                yValues[j][0] = y0[j]
            }
            
            var x = x0
            var y = y0.copyOf()
            
            for (i in 1..steps) {
                val k1 = DoubleArray(dimension) { j -> functions[j](x, y) }
                
                val yTemp2 = DoubleArray(dimension)
                for (j in 0 until dimension) {
                    yTemp2[j] = y[j] + stepSize/2 * k1[j]
                }
                val k2 = DoubleArray(dimension) { j -> functions[j](x + stepSize/2, yTemp2) }
                
                val yTemp3 = DoubleArray(dimension)
                for (j in 0 until dimension) {
                    yTemp3[j] = y[j] + stepSize/2 * k2[j]
                }
                val k3 = DoubleArray(dimension) { j -> functions[j](x + stepSize/2, yTemp3) }
                
                val yTemp4 = DoubleArray(dimension)
                for (j in 0 until dimension) {
                    yTemp4[j] = y[j] + stepSize * k3[j]
                }
                val k4 = DoubleArray(dimension) { j -> functions[j](x + stepSize, yTemp4) }
                
                for (j in 0 until dimension) {
                    y[j] += stepSize * (k1[j] + 2*k2[j] + 2*k3[j] + k4[j]) / 6
                    yValues[j][i] = y[j]
                }
                
                x += stepSize
                xValues[i] = x
            }
            
            return Pair(xValues, yValues)
        }
    }
    
    /**
     * Represents a second-order ODE of the form d²y/dx² = f(x, y, dy/dx)
     * Can be converted to a system of first-order ODEs
     */
    class SecondOrderODE(val function: (Double, Double, Double) -> Double) {
        /**
         * Converts to a system of first-order ODEs and solves
         * @param x0 Initial x value
         * @param y0 Initial y value
         * @param yPrime0 Initial dy/dx value
         * @param xEnd End x value
         * @param stepSize Step size for numerical integration
         * @return Triple of x, y, and dy/dx arrays containing the solution points
         */
        fun solve(x0: Double, y0: Double, yPrime0: Double, xEnd: Double, stepSize: Double): Triple<DoubleArray, DoubleArray, DoubleArray> {
            // Convert to system of first-order ODEs
            val system = SystemOfODEs(listOf(
                { x, y -> y[1] }, // dy/dx = y'
                { x, y -> function(x, y[0], y[1]) } // dy'/dx = f(x, y, y')
            ))
            
            val (xValues, yArrays) = system.solveRungeKutta4(x0, doubleArrayOf(y0, yPrime0), xEnd, stepSize)
            
            return Triple(xValues, yArrays[0], yArrays[1])
        }
    }
    
    /**
     * Partial Differential Equation solver using finite difference method
     * Solves the heat equation: ∂u/∂t = α ∂²u/∂x²
     */
    class HeatEquation(val alpha: Double) {
        /**
         * Solves the 1D heat equation using explicit finite difference method
         * @param initialCondition Initial temperature distribution function u(x, 0)
         * @param boundaryCondition Boundary conditions function (t) -> Pair(u(0, t), u(L, t))
         * @param xLength Length of the spatial domain
         * @param tMax Maximum time
         * @param nx Number of spatial grid points
         * @param nt Number of time grid points
         * @return 2D array representing u(x, t) at grid points
         */
        fun solve1D(
            initialCondition: (Double) -> Double,
            boundaryCondition: (Double) -> Pair<Double, Double>,
            xLength: Double,
            tMax: Double,
            nx: Int,
            nt: Int
        ): Array<DoubleArray> {
            val dx = xLength / (nx - 1)
            val dt = tMax / (nt - 1)
            
            // Check stability condition
            val r = alpha * dt / (dx * dx)
            require(r <= 0.5) { "Stability condition not met: α*dt/(dx²) = $r > 0.5" }
            
            // Initialize solution array
            val u = Array(nt) { DoubleArray(nx) }
            
            // Set initial condition
            for (i in 0 until nx) {
                val x = i * dx
                u[0][i] = initialCondition(x)
            }
            
            // Time stepping
            for (j in 0 until nt - 1) {
                val t = j * dt
                val (leftBC, rightBC) = boundaryCondition(t)
                
                // Set boundary conditions
                u[j+1][0] = leftBC
                u[j+1][nx-1] = rightBC
                
                // Update interior points
                for (i in 1 until nx - 1) {
                    u[j+1][i] = u[j][i] + r * (u[j][i+1] - 2*u[j][i] + u[j][i-1])
                }
            }
            
            return u
        }
        
        /**
         * Solves the 2D heat equation using explicit finite difference method
         * @param initialCondition Initial temperature distribution function u(x, y, 0)
         * @param boundaryCondition Boundary conditions function
         * @param xLength Length in x direction
         * @param yLength Length in y direction
         * @param tMax Maximum time
         * @param nx Number of grid points in x direction
         * @param ny Number of grid points in y direction
         * @param nt Number of time grid points
         * @return 3D array representing u(x, y, t) at grid points
         */
        fun solve2D(
            initialCondition: (Double, Double) -> Double,
            boundaryCondition: (Double, Double, Double) -> Double,
            xLength: Double,
            yLength: Double,
            tMax: Double,
            nx: Int,
            ny: Int,
            nt: Int
        ): Array<Array<DoubleArray>> {
            val dx = xLength / (nx - 1)
            val dy = yLength / (ny - 1)
            val dt = tMax / (nt - 1)
            
            // Check stability condition
            val r = alpha * dt * (1/(dx*dx) + 1/(dy*dy))
            require(r <= 0.5) { "Stability condition not met" }
            
            // Initialize solution array
            val u = Array(nt) { Array(nx) { DoubleArray(ny) } }
            
            // Set initial condition
            for (i in 0 until nx) {
                val x = i * dx
                for (j in 0 until ny) {
                    val y = j * dy
                    u[0][i][j] = initialCondition(x, y)
                }
            }
            
            // Time stepping
            for (k in 0 until nt - 1) {
                val t = k * dt
                
                // Set boundary conditions
                for (i in 0 until nx) {
                    val x = i * dx
                    u[k+1][i][0] = boundaryCondition(x, 0.0, t)
                    u[k+1][i][ny-1] = boundaryCondition(x, yLength, t)
                }
                
                for (j in 0 until ny) {
                    val y = j * dy
                    u[k+1][0][j] = boundaryCondition(0.0, y, t)
                    u[k+1][nx-1][j] = boundaryCondition(xLength, y, t)
                }
                
                // Update interior points
                for (i in 1 until nx - 1) {
                    for (j in 1 until ny - 1) {
                        val uxx = (u[k][i+1][j] - 2*u[k][i][j] + u[k][i-1][j]) / (dx*dx)
                        val uyy = (u[k][i][j+1] - 2*u[k][i][j] + u[k][i][j-1]) / (dy*dy)
                        u[k+1][i][j] = u[k][i][j] + alpha * dt * (uxx + uyy)
                    }
                }
            }
            
            return u
        }
    }
    
    /**
     * Solves the wave equation: ∂²u/∂t² = c² ∂²u/∂x²
     */
    class WaveEquation(val c: Double) {
        /**
         * Solves the 1D wave equation using explicit finite difference method
         * @param initialPosition Initial position u(x, 0)
         * @param initialVelocity Initial velocity ∂u/∂t(x, 0)
         * @param boundaryCondition Boundary conditions function (t) -> Pair(u(0, t), u(L, t))
         * @param xLength Length of the spatial domain
         * @param tMax Maximum time
         * @param nx Number of spatial grid points
         * @param nt Number of time grid points
         * @return 2D array representing u(x, t) at grid points
         */
        fun solve1D(
            initialPosition: (Double) -> Double,
            initialVelocity: (Double) -> Double,
            boundaryCondition: (Double) -> Pair<Double, Double>,
            xLength: Double,
            tMax: Double,
            nx: Int,
            nt: Int
        ): Array<DoubleArray> {
            val dx = xLength / (nx - 1)
            val dt = tMax / (nt - 1)
            
            // Check stability condition (CFL condition)
            val r = c * dt / dx
            require(r <= 1.0) { "Stability condition not met: c*dt/dx = $r > 1.0" }
            
            // Initialize solution array
            val u = Array(nt) { DoubleArray(nx) }
            
            // Set initial position
            for (i in 0 until nx) {
                val x = i * dx
                u[0][i] = initialPosition(x)
            }
            
            // Set initial velocity (using central difference for first time step)
            for (i in 1 until nx - 1) {
                val x = i * dx
                u[1][i] = u[0][i] + dt * initialVelocity(x) + 
                          0.5 * r * r * (u[0][i+1] - 2*u[0][i] + u[0][i-1])
            }
            
            // Set boundary conditions for first time step
            val (leftBC0, rightBC0) = boundaryCondition(0.0)
            u[1][0] = leftBC0
            u[1][nx-1] = rightBC0
            
            // Time stepping
            for (j in 1 until nt - 1) {
                val t = (j + 1) * dt
                val (leftBC, rightBC) = boundaryCondition(t)
                
                // Set boundary conditions
                u[j+1][0] = leftBC
                u[j+1][nx-1] = rightBC
                
                // Update interior points using wave equation discretization
                for (i in 1 until nx - 1) {
                    u[j+1][i] = 2*u[j][i] - u[j-1][i] + 
                                r*r * (u[j][i+1] - 2*u[j][i] + u[j][i-1])
                }
            }
            
            return u
        }
        
        /**
         * Solves the 2D wave equation using explicit finite difference method
         * @param initialPosition Initial position u(x, y, 0)
         * @param initialVelocity Initial velocity ∂u/∂t(x, y, 0)
         * @param boundaryCondition Boundary conditions function
         * @param xLength Length in x direction
         * @param yLength Length in y direction
         * @param tMax Maximum time
         * @param nx Number of grid points in x direction
         * @param ny Number of grid points in y direction
         * @param nt Number of time grid points
         * @return 3D array representing u(x, y, t) at grid points
         */
        fun solve2D(
            initialPosition: (Double, Double) -> Double,
            initialVelocity: (Double, Double) -> Double,
            boundaryCondition: (Double, Double, Double) -> Double,
            xLength: Double,
            yLength: Double,
            tMax: Double,
            nx: Int,
            ny: Int,
            nt: Int
        ): Array<Array<DoubleArray>> {
            val dx = xLength / (nx - 1)
            val dy = yLength / (ny - 1)
            val dt = tMax / (nt - 1)
            
            // Check stability condition
            val r = c * dt * sqrt(1/(dx*dx) + 1/(dy*dy))
            require(r <= 1.0) { "Stability condition not met" }
            
            // Initialize solution array
            val u = Array(nt) { Array(nx) { DoubleArray(ny) } }
            
            // Set initial position
            for (i in 0 until nx) {
                val x = i * dx
                for (j in 0 until ny) {
                    val y = j * dy
                    u[0][i][j] = initialPosition(x, y)
                }
            }
            
            // Set initial velocity (using central difference for first time step)
            for (i in 1 until nx - 1) {
                val x = i * dx
                for (j in 1 until ny - 1) {
                    val y = j * dy
                    val laplacian = (u[0][i+1][j] - 2*u[0][i][j] + u[0][i-1][j])/(dx*dx) +
                                    (u[0][i][j+1] - 2*u[0][i][j] + u[0][i][j-1])/(dy*dy)
                    u[1][i][j] = u[0][i][j] + dt * initialVelocity(x, y) + 
                                0.5 * c*c * dt*dt * laplacian
                }
            }
            
            // Set boundary conditions for first time step
            for (i in 0 until nx) {
                val x = i * dx
                u[1][i][0] = boundaryCondition(x, 0.0, dt)
                u[1][i][ny-1] = boundaryCondition(x, yLength, dt)
            }
            
            for (j in 0 until ny) {
                val y = j * dy
                u[1][0][j] = boundaryCondition(0.0, y, dt)
                u[1][nx-1][j] = boundaryCondition(xLength, y, dt)
            }
            
            // Time stepping
            for (k in 1 until nt - 1) {
                val t = (k + 1) * dt
                
                // Set boundary conditions
                for (i in 0 until nx) {
                    val x = i * dx
                    u[k+1][i][0] = boundaryCondition(x, 0.0, t)
                    u[k+1][i][ny-1] = boundaryCondition(x, yLength, t)
                }
                
                for (j in 0 until ny) {
                    val y = j * dy
                    u[k+1][0][j] = boundaryCondition(0.0, y, t)
                    u[k+1][nx-1][j] = boundaryCondition(xLength, y, t)
                }
                
                // Update interior points
                for (i in 1 until nx - 1) {
                    for (j in 1 until ny - 1) {
                        val uxx = (u[k][i+1][j] - 2*u[k][i][j] + u[k][i-1][j])/(dx*dx)
                        val uyy = (u[k][i][j+1] - 2*u[k][i][j] + u[k][i][j-1])/(dy*dy)
                        u[k+1][i][j] = 2*u[k][i][j] - u[k-1][i][j] + 
                                      c*c * dt*dt * (uxx + uyy)
                    }
                }
            }
            
            return u
        }
    }
}

