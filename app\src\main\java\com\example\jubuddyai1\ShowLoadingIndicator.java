package com.example.jubuddyai1;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.WindowManager;

public class ShowLoadingIndicator {
    private Dialog loadingDialog;

    public ShowLoadingIndicator(Context context) {
        loadingDialog = new Dialog(context);
        loadingDialog.setContentView(R.layout.loading_dialog);
        
        if (loadingDialog.getWindow() != null) {
            loadingDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            loadingDialog.getWindow().setLayout(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    WindowManager.LayoutParams.WRAP_CONTENT
            );
            loadingDialog.getWindow().setGravity(Gravity.CENTER);
        }
        
        loadingDialog.setCancelable(false);
    }

    public void show() {
        if (!loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    public void dismiss() {
        if (loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
    }
} 