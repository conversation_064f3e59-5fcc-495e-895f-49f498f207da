<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="assistant_message_bg">#F5F5F5</color>
    <color name="black">#000000</color>
    <color name="ic_launcher_background">#FFFFF9</color>
    <color name="linkColor">#2196F3</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="user_message_bg">#E3F2FD</color>
    <color name="white">#FFFFFF</color>
    <dimen name="card_corner_radius">8dp</dimen>
    <dimen name="message_margin_large">48dp</dimen>
    <dimen name="message_margin_small">16dp</dimen>
    <string name="analyzing_image">Analyzing image...</string>
    <string name="analyzing_wait">Analyzing image, please wait...</string>
    <string name="app_name">JU<PERSON>uddy AI</string>
    <string name="ask_about_image">Ask about this image...</string>
    <string name="ask_about_text">Ask about this text...</string>
    <string name="btn_sign_in">Sign In</string>
    <string name="btn_sign_in_google">Sign in with Google</string>
    <string name="default_web_client_id" translatable="false">***********-0rc699nkm1d0jkt8qbfpnmd16rrb6d0u.apps.googleusercontent.com</string>
    <string name="error_invalid_email">Enter a valid email address</string>
    <string name="error_invalid_password">Password must be at least 6 characters</string>
    <string name="error_processing_image">Error processing image: %1$s</string>
    <string name="gcm_defaultSenderId" translatable="false">***********</string>
    <string name="google_api_key" translatable="false">AIzaSyB45IgHu_AfABtuN5lD_jI-1DPgH639sgg</string>
    <string name="google_app_id" translatable="false">1:***********:android:cbaeec9e2987552d29b40e</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyB45IgHu_AfABtuN5lD_jI-1DPgH639sgg</string>
    <string name="google_storage_bucket" translatable="false">jubuddy-ai.firebasestorage.app</string>
    <string name="hint_email">Email</string>
    <string name="hint_password">Password</string>
    <string name="project_id" translatable="false">jubuddy-ai</string>
    <string name="text_create_account">New user? Create an account</string>
    <string name="text_or">OR</string>
    <string name="type_message">Type a message...</string>
    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.JUBuddyAI1" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@android:color/black</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@android:color/black</item>
        
        <item name="android:navigationBarColor">@android:color/black</item>
        
        <item name="android:navigationBarDividerColor">@android:color/transparent</item>
        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
    </style>
    <style name="TransparentDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
</resources>