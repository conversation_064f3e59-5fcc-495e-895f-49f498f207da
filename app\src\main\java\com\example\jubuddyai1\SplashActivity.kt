package com.example.jubuddyai1

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth

class SplashActivity : AppCompatActivity() {

    private lateinit var sharedPreferences: android.content.SharedPreferences
    private val TAG = "SplashActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // Initialize Firebase (important to do this early)
            FirebaseApp.initializeApp(this)
            
            // Set up the UI
            hideSystemUI()
            setContentView(R.layout.activity_splash)
            
            // Initialize shared preferences
            sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
            
            // Delayed navigation after splash animation
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    navigateToNextScreen()
                } catch (e: Exception) {
                    Log.e(TAG, "Error navigating: ${e.message}")
                    // Fallback to SignInActivity in case of error
                    startActivity(Intent(this, SignInActivity::class.java))
                    finish()
                }
            }, 2000) // 2 seconds delay
        } catch (e: Exception) {
            Log.e(TAG, "Error in onCreate: ${e.message}")
            // Even if there's an error, try to move to SignInActivity
            startActivity(Intent(this, SignInActivity::class.java))
            finish()
        }
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }

    private fun navigateToNextScreen() {
        try {
            // Get authentication state safely
            val currentUser = FirebaseAuth.getInstance().currentUser
            val termsAccepted = sharedPreferences.getBoolean("termsAccepted", false)
            
            Log.d(TAG, "Current user: $currentUser, Terms accepted: $termsAccepted")
            
            if (currentUser == null) {
                // Not signed in, go to sign in
                startActivity(Intent(this, SignInActivity::class.java))
            } else if (!termsAccepted) {
                // Signed in but terms not accepted - only shown once
                startActivity(Intent(this, TermsActivity::class.java))
            } else {
                // Both signed in and terms accepted
                startActivity(Intent(this, MainActivity::class.java))
            }
            
            // Always finish the splash activity
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Navigation error: ${e.message}")
            // Fallback to SignInActivity in case of error
            startActivity(Intent(this, SignInActivity::class.java))
            finish()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }
} 