<dependencies>
  <compile
      roots="com.google.mlkit:object-detection:17.0.0@aar,com.google.mlkit:object-detection-custom:17.0.0@aar,com.google.mlkit:object-detection-common:18.0.0@aar,com.google.mlkit:image-labeling:17.0.7@aar,com.google.mlkit:vision-internal-vkp:18.2.2@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.firebaseui:firebase-ui-auth:8.0.2@aar,com.google.firebase:firebase-auth:23.0.0@aar,com.google.firebase:firebase-analytics:22.0.0@aar,com.google.android.gms:play-services-measurement-api:22.0.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.squareup.okio:okio-jvm:3.6.0@jar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.android.recaptcha:recaptcha:18.4.0@aar,com.google.android.material:material:1.11.0@aar,androidx.appcompat:appcompat:1.6.1@aar,com.google.android.gms:play-services-auth:20.7.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,com.google.mlkit:text-recognition:16.0.0@aar,com.google.android.gms:play-services-mlkit-text-recognition:19.0.0@aar,com.google.mlkit:text-recognition-bundled-common:16.0.0@aar,com.google.mlkit:image-labeling-default-common:17.0.0@aar,com.google.mlkit:image-labeling-common:18.0.0@aar,com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.8.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.4@aar,com.google.android.gms:play-services-fido:20.1.0@aar,com.google.firebase:firebase-appcheck-interop:17.0.0@aar,com.google.android.gms:play-services-ads:22.6.0@aar,com.google.android.gms:play-services-appset:16.0.1@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.android.play:integrity:1.2.0@aar,com.google.mlkit:vision-interfaces:16.2.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-measurement:22.0.0@aar,com.google.android.gms:play-services-measurement-sdk:22.0.0@aar,com.google.android.gms:play-services-ads-lite:22.6.0@aar,com.google.android.gms:play-services-ads-base:22.6.0@aar,com.google.android.gms:play-services-measurement-impl:22.0.0@aar,com.google.android.ump:user-messaging-platform:2.1.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.0.0@aar,com.google.android.gms:play-services-measurement-base:22.0.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.8.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.browser:browser:1.4.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.lifecycle:lifecycle-service:2.6.1@aar,androidx.lifecycle:lifecycle-process:2.6.1@aar,androidx.work:work-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.6.1@aar,androidx.lifecycle:lifecycle-livedata:2.6.1@aar,androidx.lifecycle:lifecycle-common:2.6.1@jar,androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.itextpdf:itextg:5.5.10@jar,com.google.code.gson:gson:2.10.1@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,androidx.savedstate:savedstate:1.2.1@aar,androidx.annotation:annotation-experimental:1.3.0@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,com.google.android.datatransport:transport-backend-cct:2.3.3@aar,com.google.firebase:firebase-encoders-json:17.1.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.android.datatransport:transport-runtime:2.2.6@aar,com.google.android.datatransport:transport-api:2.2.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.exifinterface:exifinterface:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:guava:31.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar">
    <dependency
        name="com.google.mlkit:object-detection:17.0.0@aar"
        simpleName="com.google.mlkit:object-detection"/>
    <dependency
        name="com.google.mlkit:object-detection-custom:17.0.0@aar"
        simpleName="com.google.mlkit:object-detection-custom"/>
    <dependency
        name="com.google.mlkit:object-detection-common:18.0.0@aar"
        simpleName="com.google.mlkit:object-detection-common"/>
    <dependency
        name="com.google.mlkit:image-labeling:17.0.7@aar"
        simpleName="com.google.mlkit:image-labeling"/>
    <dependency
        name="com.google.mlkit:vision-internal-vkp:18.2.2@aar"
        simpleName="com.google.mlkit:vision-internal-vkp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.firebaseui:firebase-ui-auth:8.0.2@aar"
        simpleName="com.firebaseui:firebase-ui-auth"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.0.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.0.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.4.0@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.material:material:1.11.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-auth:20.7.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.mlkit:text-recognition:16.0.0@aar"
        simpleName="com.google.mlkit:text-recognition"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-text-recognition:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-text-recognition"/>
    <dependency
        name="com.google.mlkit:text-recognition-bundled-common:16.0.0@aar"
        simpleName="com.google.mlkit:text-recognition-bundled-common"/>
    <dependency
        name="com.google.mlkit:image-labeling-default-common:17.0.0@aar"
        simpleName="com.google.mlkit:image-labeling-default-common"/>
    <dependency
        name="com.google.mlkit:image-labeling-common:18.0.0@aar"
        simpleName="com.google.mlkit:image-labeling-common"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-text-recognition-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.8.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.4@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.android.gms:play-services-ads:22.6.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.1@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.play:integrity:1.2.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.2.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-ads-lite:22.6.0@aar"
        simpleName="com.google.android.gms:play-services-ads-lite"/>
    <dependency
        name="com.google.android.gms:play-services-ads-base:22.6.0@aar"
        simpleName="com.google.android.gms:play-services-ads-base"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:2.1.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.browser:browser:1.4.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.work:work-runtime:2.7.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.itextpdf:itextg:5.5.10@jar"
        simpleName="com.itextpdf:itextg"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:2.2.1@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.0.0@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="com.google.mlkit:object-detection:17.0.0@aar,com.google.mlkit:object-detection-custom:17.0.0@aar,com.google.mlkit:object-detection-common:18.0.0@aar,com.google.mlkit:image-labeling:17.0.7@aar,com.google.mlkit:vision-internal-vkp:18.2.2@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.firebaseui:firebase-ui-auth:8.0.2@aar,com.google.android.material:material:1.11.0@aar,com.squareup.okio:okio-jvm:3.6.0@jar,com.google.firebase:firebase-auth:23.0.0@aar,com.google.firebase:firebase-analytics:22.0.0@aar,com.google.android.gms:play-services-measurement-api:22.0.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,com.google.android.libraries.identity.googleid:googleid:1.1.0@aar,com.google.android.recaptcha:recaptcha:18.4.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,com.google.android.gms:play-services-auth:20.7.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,com.google.mlkit:text-recognition:16.0.0@aar,com.google.android.gms:play-services-mlkit-text-recognition:19.0.0@aar,com.google.mlkit:text-recognition-bundled-common:16.0.0@aar,com.google.mlkit:image-labeling-default-common:17.0.0@aar,com.google.mlkit:image-labeling-common:18.0.0@aar,com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.8.0@aar,com.google.android.gms:play-services-auth-base:18.0.4@aar,com.google.android.gms:play-services-ads:22.6.0@aar,com.google.android.gms:play-services-appset:16.0.1@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.firebase:firebase-appcheck-interop:17.0.0@aar,com.google.android.gms:play-services-fido:20.1.0@aar,com.google.android.gms:play-services-base:18.1.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.google.android.gms:play-services-ads-lite:22.6.0@aar,com.google.android.gms:play-services-ads-base:22.6.0@aar,com.google.android.gms:play-services-measurement:22.0.0@aar,com.google.android.ump:user-messaging-platform:2.1.0@aar,com.google.android.gms:play-services-measurement-sdk:22.0.0@aar,com.google.android.gms:play-services-measurement-impl:22.0.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.play:integrity:1.2.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.mlkit:vision-interfaces:16.2.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.0.0@aar,com.google.android.gms:play-services-measurement-base:22.0.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.8.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.browser:browser:1.4.0@aar,androidx.browser:browser:1.4.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.media:media:1.0.0@aar,androidx.work:work-runtime:2.7.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-service:2.6.1@aar,androidx.lifecycle:lifecycle-service:2.6.1@aar,androidx.lifecycle:lifecycle-process:2.6.1@aar,androidx.lifecycle:lifecycle-process:2.6.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar,androidx.lifecycle:lifecycle-livedata:2.6.1@aar,androidx.lifecycle:lifecycle-livedata:2.6.1@aar,androidx.lifecycle:lifecycle-common:2.6.1@jar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-runtime:2.6.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,com.itextpdf:itextg:5.5.10@jar,com.google.code.gson:gson:2.10.1@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.0@jar,com.google.android.datatransport:transport-backend-cct:2.3.3@aar,com.google.firebase:firebase-encoders-json:17.1.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.0@jar,androidx.annotation:annotation-experimental:1.3.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,com.google.android.datatransport:transport-runtime:2.2.6@aar,com.google.android.datatransport:transport-api:2.2.1@aar,com.google.firebase:firebase-components:18.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.profileinstaller:profileinstaller:1.3.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.room:room-runtime:2.2.5@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.exifinterface:exifinterface:1.0.0@aar,androidx.sqlite:sqlite-framework:2.1.0@aar,androidx.sqlite:sqlite:2.1.0@aar,androidx.room:room-common:2.2.5@jar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar,com.google.guava:guava:31.1-android@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.firebase:firebase-annotations:16.2.0@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar,org.jetbrains:annotations:23.0.0@jar,javax.inject:javax.inject:1@jar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.12.0@jar,com.google.j2objc:j2objc-annotations:1.3@jar">
    <dependency
        name="com.google.mlkit:object-detection:17.0.0@aar"
        simpleName="com.google.mlkit:object-detection"/>
    <dependency
        name="com.google.mlkit:object-detection-custom:17.0.0@aar"
        simpleName="com.google.mlkit:object-detection-custom"/>
    <dependency
        name="com.google.mlkit:object-detection-common:18.0.0@aar"
        simpleName="com.google.mlkit:object-detection-common"/>
    <dependency
        name="com.google.mlkit:image-labeling:17.0.7@aar"
        simpleName="com.google.mlkit:image-labeling"/>
    <dependency
        name="com.google.mlkit:vision-internal-vkp:18.2.2@aar"
        simpleName="com.google.mlkit:vision-internal-vkp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.firebaseui:firebase-ui-auth:8.0.2@aar"
        simpleName="com.firebaseui:firebase-ui-auth"/>
    <dependency
        name="com.google.android.material:material:1.11.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.0.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.0.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.4.0@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-auth:20.7.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.mlkit:text-recognition:16.0.0@aar"
        simpleName="com.google.mlkit:text-recognition"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-text-recognition:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-text-recognition"/>
    <dependency
        name="com.google.mlkit:text-recognition-bundled-common:16.0.0@aar"
        simpleName="com.google.mlkit:text-recognition-bundled-common"/>
    <dependency
        name="com.google.mlkit:image-labeling-default-common:17.0.0@aar"
        simpleName="com.google.mlkit:image-labeling-default-common"/>
    <dependency
        name="com.google.mlkit:image-labeling-common:18.0.0@aar"
        simpleName="com.google.mlkit:image-labeling-common"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-text-recognition-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.8.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.4@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-ads:22.6.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.android.gms:play-services-appset:16.0.1@aar"
        simpleName="com.google.android.gms:play-services-appset"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.google.android.gms:play-services-ads-lite:22.6.0@aar"
        simpleName="com.google.android.gms:play-services-ads-lite"/>
    <dependency
        name="com.google.android.gms:play-services-ads-base:22.6.0@aar"
        simpleName="com.google.android.gms:play-services-ads-base"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:2.1.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.play:integrity:1.2.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.2.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.browser:browser:1.4.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.work:work-runtime:2.7.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="com.itextpdf:itextg:5.5.10@jar"
        simpleName="com.itextpdf:itextg"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:2.2.1@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.room:room-runtime:2.2.5@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.0.0@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.room:room-common:2.2.5@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.guava:guava:31.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.12.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:1.3@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </package>
</dependencies>
