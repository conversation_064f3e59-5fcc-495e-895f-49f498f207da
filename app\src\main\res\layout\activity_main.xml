<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/homeButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Home"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="32dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/pyqButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="PYQ"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/homeButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/syllabusButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Syllabus"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pyqButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/notesButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="NOTES"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/syllabusButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/storeButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="STORE"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notesButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/jubuddyButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="JUBuddy AI"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/storeButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/settingsButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="SETTINGS"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/jubuddyButton"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginTop="16dp" />

    <FrameLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>