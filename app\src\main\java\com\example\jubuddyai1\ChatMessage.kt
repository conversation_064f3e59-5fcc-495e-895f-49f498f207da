package com.example.jubuddyai1

import android.net.Uri
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import com.google.gson.annotations.SerializedName
import java.io.Serializable

@Parcelize
data class ChatMessage(
    @SerializedName("content")
    val content: String,
    
    @SerializedName("isUser")
    val isUser: Boolean,
    
    @SerializedName("imageUri")
    val imageUri: Uri? = null
) : Parcelable, Serializable {
    constructor(content: String, isUser: Boolean, imageUriString: String?) : 
        this(content, isUser, imageUriString?.let { Uri.parse(it) })
        
    fun getImageUriString(): String? {
        return imageUri?.toString()
    }
} 